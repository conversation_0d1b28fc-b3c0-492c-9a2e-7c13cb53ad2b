# -*- coding: utf-8 -*-
"""
进度显示组件
提供任务进度跟踪和显示功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar, 
    QPushButton, QFrame, QScrollArea, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont
from typing import Dict, Optional

from ui.styles import StyleManager
from utils.task_queue import TaskQueue, Task, TaskStatus
from utils.helpers import DateTimeHelper


class TaskProgressItem(QFrame):
    """单个任务进度项"""
    
    # 信号定义
    cancel_requested = pyqtSignal(str)  # task_id
    
    def __init__(self, task: Task, parent=None):
        """
        初始化任务进度项
        
        Args:
            task: 任务对象
            parent: 父组件
        """
        super().__init__(parent)
        self.task = task
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(6)
        
        # 任务信息行
        info_layout = QHBoxLayout()
        
        # 任务名称
        self.name_label = QLabel(self.task.name)
        name_font = QFont()
        name_font.setBold(True)
        self.name_label.setFont(name_font)
        
        # 状态标签
        self.status_label = QLabel()
        self.update_status_label()
        
        # 取消按钮
        self.cancel_btn = StyleManager.create_button("取消", "danger")
        self.cancel_btn.setMaximumWidth(60)
        self.cancel_btn.clicked.connect(lambda: self.cancel_requested.emit(self.task.id))
        
        info_layout.addWidget(self.name_label)
        info_layout.addStretch()
        info_layout.addWidget(self.status_label)
        info_layout.addWidget(self.cancel_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(int(self.task.progress))
        
        # 描述标签
        self.desc_label = QLabel(self.task.description or "正在处理...")
        self.desc_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')};")
        
        layout.addLayout(info_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.desc_label)
        
        self.setLayout(layout)
        
        # 设置样式
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.get_color('BACKGROUND')};
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 6px;
                margin: 2px;
            }}
        """)
    
    def update_status_label(self):
        """更新状态标签"""
        status_config = {
            TaskStatus.PENDING: {"text": "等待中", "color": StyleManager.get_color('WARNING')},
            TaskStatus.RUNNING: {"text": "执行中", "color": StyleManager.get_color('INFO')},
            TaskStatus.COMPLETED: {"text": "已完成", "color": StyleManager.get_color('SUCCESS')},
            TaskStatus.FAILED: {"text": "失败", "color": StyleManager.get_color('DANGER')},
            TaskStatus.CANCELLED: {"text": "已取消", "color": StyleManager.get_color('NEUTRAL_DARK')}
        }
        
        config = status_config.get(self.task.status, status_config[TaskStatus.PENDING])
        self.status_label.setText(config["text"])
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {config["color"]};
                font-weight: bold;
                padding: 2px 8px;
                border-radius: 4px;
                background: {config["color"]}20;
            }}
        """)
        
        # 根据状态显示/隐藏取消按钮
        if self.task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            self.cancel_btn.hide()
        else:
            self.cancel_btn.show()
    
    def update_progress(self, progress: float):
        """
        更新进度
        
        Args:
            progress: 进度值 (0-100)
        """
        self.task.progress = progress
        self.progress_bar.setValue(int(progress))
    
    def update_task(self, task: Task):
        """
        更新任务信息
        
        Args:
            task: 更新后的任务对象
        """
        self.task = task
        self.name_label.setText(task.name)
        self.desc_label.setText(task.description or "正在处理...")
        self.progress_bar.setValue(int(task.progress))
        self.update_status_label()


class ProgressWidget(QWidget):
    """进度显示组件"""
    
    def __init__(self, task_queue: TaskQueue, parent=None):
        """
        初始化进度组件
        
        Args:
            task_queue: 任务队列
            parent: 父组件
        """
        super().__init__(parent)
        self.task_queue = task_queue
        self.task_items: Dict[str, TaskProgressItem] = {}
        
        self.init_ui()
        self.connect_signals()
        
        # 定时更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # 标题栏
        header_layout = QHBoxLayout()
        
        title_label = QLabel("任务进度")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        # 统计信息
        self.stats_label = QLabel()
        self.update_stats_label()
        
        # 清除按钮
        self.clear_btn = StyleManager.create_button("清除已完成", "")
        self.clear_btn.clicked.connect(self.clear_completed_tasks)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.stats_label)
        header_layout.addWidget(self.clear_btn)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 任务列表容器
        self.task_container = QWidget()
        self.task_layout = QVBoxLayout(self.task_container)
        self.task_layout.setContentsMargins(5, 5, 5, 5)
        self.task_layout.setSpacing(5)
        self.task_layout.addStretch()  # 底部弹性空间
        
        scroll_area.setWidget(self.task_container)
        
        layout.addLayout(header_layout)
        layout.addWidget(scroll_area)
        
        self.setLayout(layout)
        
        # 设置最小尺寸
        self.setMinimumHeight(200)
    
    def connect_signals(self):
        """连接信号"""
        self.task_queue.task_added.connect(self.on_task_added)
        self.task_queue.task_started.connect(self.on_task_started)
        self.task_queue.task_progress.connect(self.on_task_progress)
        self.task_queue.task_completed.connect(self.on_task_completed)
        self.task_queue.task_failed.connect(self.on_task_failed)
        self.task_queue.queue_status_changed.connect(self.on_queue_status_changed)
    
    def on_task_added(self, task_id: str):
        """
        任务添加事件处理
        
        Args:
            task_id: 任务ID
        """
        task = self.task_queue.get_task(task_id)
        if task and task_id not in self.task_items:
            item = TaskProgressItem(task)
            item.cancel_requested.connect(self.cancel_task)
            
            # 插入到布局中（在弹性空间之前）
            self.task_layout.insertWidget(self.task_layout.count() - 1, item)
            self.task_items[task_id] = item
    
    def on_task_started(self, task_id: str):
        """
        任务开始事件处理
        
        Args:
            task_id: 任务ID
        """
        if task_id in self.task_items:
            task = self.task_queue.get_task(task_id)
            if task:
                self.task_items[task_id].update_task(task)
    
    def on_task_progress(self, task_id: str, progress: float):
        """
        任务进度更新事件处理
        
        Args:
            task_id: 任务ID
            progress: 进度值
        """
        if task_id in self.task_items:
            self.task_items[task_id].update_progress(progress)
    
    def on_task_completed(self, task_id: str, result):
        """
        任务完成事件处理
        
        Args:
            task_id: 任务ID
            result: 任务结果
        """
        if task_id in self.task_items:
            task = self.task_queue.get_task(task_id)
            if task:
                self.task_items[task_id].update_task(task)
    
    def on_task_failed(self, task_id: str, error_message: str):
        """
        任务失败事件处理
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
        """
        if task_id in self.task_items:
            task = self.task_queue.get_task(task_id)
            if task:
                self.task_items[task_id].update_task(task)
    
    def on_queue_status_changed(self, pending_count: int, running_count: int):
        """
        队列状态变更事件处理
        
        Args:
            pending_count: 待处理任务数
            running_count: 运行中任务数
        """
        self.update_stats_label()
    
    def cancel_task(self, task_id: str):
        """
        取消任务
        
        Args:
            task_id: 任务ID
        """
        success = self.task_queue.cancel_task(task_id)
        if success and task_id in self.task_items:
            task = self.task_queue.get_task(task_id)
            if task:
                self.task_items[task_id].update_task(task)
    
    def clear_completed_tasks(self):
        """清除已完成的任务"""
        # 移除已完成任务的UI项
        completed_ids = []
        for task_id, item in self.task_items.items():
            if item.task.is_finished:
                self.task_layout.removeWidget(item)
                item.deleteLater()
                completed_ids.append(task_id)
        
        # 从字典中移除
        for task_id in completed_ids:
            del self.task_items[task_id]
        
        # 清除队列中的已完成任务
        self.task_queue.clear_completed_tasks()
    
    def update_stats_label(self):
        """更新统计标签"""
        stats = self.task_queue.get_statistics()
        text = f"总计: {stats['total_tasks']} | 等待: {stats['pending_tasks']} | 运行: {stats['running_tasks']}"
        self.stats_label.setText(text)
        self.stats_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')};")
    
    def update_display(self):
        """定时更新显示"""
        # 更新所有任务项的状态
        for task_id, item in self.task_items.items():
            task = self.task_queue.get_task(task_id)
            if task:
                item.update_task(task)
    
    def get_active_task_count(self) -> int:
        """
        获取活动任务数量
        
        Returns:
            活动任务数量
        """
        return len([item for item in self.task_items.values() if not item.task.is_finished])
