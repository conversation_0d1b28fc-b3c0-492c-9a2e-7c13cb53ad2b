# -*- coding: utf-8 -*-
"""
增强版API客户端
提供异步API调用、缓存机制和完善的错误处理
"""

import asyncio
import time
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import requests
import aiohttp
from cachetools import TTLCache

from config.settings import Config
from config.constants import API_ENDPOINTS, PLAN_IDS
from utils.helpers import EmailValidator, URLHelper
from utils.logger import Logger
from utils.security import SecurityManager
from .models import TeamData, APIResponse


class APIClient:
    """增强版API客户端"""
    
    def __init__(self, config: Config, logger: Optional[Logger] = None, 
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化API客户端
        
        Args:
            config: 配置管理器
            logger: 日志管理器
            security_manager: 安全管理器
        """
        self.config = config
        self.logger = logger or Logger("APIClient")
        self.security_manager = security_manager or SecurityManager()
        
        # 同步会话
        self.session = requests.Session()
        self._setup_session()
        
        # 异步会话（延迟初始化）
        self._async_session = None
        
        # 缓存系统
        self.cache = TTLCache(maxsize=100, ttl=300)  # 5分钟TTL
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'last_request_time': None
        }
        
        self.logger.info("API客户端", "API客户端初始化完成")
    
    def _setup_session(self):
        """设置会话配置"""
        # 设置超时
        timeout = self.config.get('api.timeout', 30)
        self.session.timeout = timeout
        
        # 设置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.logger.debug("会话配置", f"会话超时设置为 {timeout} 秒")
    
    def _get_headers(self) -> Dict[str, str]:
        """
        获取请求头
        
        Returns:
            请求头字典
        """
        headers = self.config.get('api.headers', {}).copy()
        
        # 解密Cookie（如果启用了加密）
        if 'cookie' in headers and headers['cookie']:
            if self.config.get('security.encrypt_cookies', False):
                headers['cookie'] = self.security_manager.decrypt_cookie(headers['cookie'])
        
        # 为POST请求添加content-type
        if 'content-type' not in headers:
            headers['content-type'] = 'application/json'
        
        return headers
    
    def _log_request(self, method: str, url: str, status_code: int = None, 
                    response_time: float = None, error: str = None):
        """
        记录API请求日志
        
        Args:
            method: HTTP方法
            url: 请求URL
            status_code: 响应状态码
            response_time: 响应时间
            error: 错误信息
        """
        self.request_stats['total_requests'] += 1
        self.request_stats['last_request_time'] = datetime.now()
        
        if error:
            self.request_stats['failed_requests'] += 1
            self.logger.error("API请求失败", f"{method} {url} - {error}")
        elif status_code and status_code >= 400:
            self.request_stats['failed_requests'] += 1
            self.logger.warning("API请求警告", f"{method} {url} -> {status_code}")
        else:
            self.request_stats['successful_requests'] += 1
            self.logger.info("API请求成功", f"{method} {url} -> {status_code}")
        
        if response_time:
            self.logger.debug("响应时间", f"{response_time:.2f}秒")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """
        发起HTTP请求的通用方法
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            API响应对象
        """
        base_url = self.config.get('api.base_url', API_ENDPOINTS['BASE_URL'])
        url = f"{base_url}{endpoint}"
        
        # 验证URL安全性
        is_valid, error_msg = URLHelper.validate_url(url)
        if not is_valid:
            return APIResponse.error_response(f"URL无效: {error_msg}")
        
        if not URLHelper.is_secure_url(url):
            self.logger.warning("安全警告", "使用非HTTPS连接可能不安全")
        
        headers = self._get_headers()
        start_time = time.time()
        
        try:
            self.logger.debug("发起请求", f"{method} {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                timeout=self.config.get('api.timeout', 30),
                **kwargs
            )
            
            response_time = time.time() - start_time
            self._log_request(method, url, response.status_code, response_time)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    return APIResponse.success_response(data, "请求成功")
                except ValueError as e:
                    return APIResponse.error_response(f"JSON解析失败: {str(e)}", 
                                                    status_code=response.status_code)
            else:
                error_msg = f"请求失败，状态码: {response.status_code}"
                if response.text:
                    error_msg += f"\n响应内容: {response.text[:200]}..."
                
                return APIResponse.error_response(error_msg, status_code=response.status_code)
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="TIMEOUT")
            
        except requests.exceptions.ConnectionError:
            error_msg = "连接失败，请检查网络连接"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="CONNECTION_ERROR")
            
        except Exception as e:
            error_msg = f"网络错误: {str(e)}"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="UNKNOWN_ERROR")
    
    def _get_cache_key(self, method: str, endpoint: str, params: Dict = None) -> str:
        """
        生成缓存键
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: 请求参数
            
        Returns:
            缓存键
        """
        key_parts = [method, endpoint]
        if params:
            # 将参数排序后加入键中
            sorted_params = sorted(params.items())
            key_parts.append(str(sorted_params))
        
        return "|".join(key_parts)
    
    def validate_email(self, email: str) -> bool:
        """
        验证邮箱格式

        Args:
            email: 邮箱地址

        Returns:
            是否有效
        """
        is_valid, _ = EmailValidator.validate_email(email)
        return is_valid

    def get_team_data(self, use_cache: bool = True) -> APIResponse:
        """
        获取团队数据

        Args:
            use_cache: 是否使用缓存

        Returns:
            API响应对象
        """
        cache_key = self._get_cache_key("GET", API_ENDPOINTS['TEAM'])

        # 检查缓存
        if use_cache and cache_key in self.cache:
            self.request_stats['cache_hits'] += 1
            self.logger.debug("缓存命中", "使用缓存的团队数据")
            cached_data = self.cache[cache_key]
            return APIResponse.success_response(cached_data, "从缓存获取数据")

        # 发起请求
        response = self._make_request("GET", API_ENDPOINTS['TEAM'])

        # 缓存成功的响应
        if response.success and use_cache:
            self.cache[cache_key] = response.data
            self.logger.debug("数据缓存", "团队数据已缓存")

        return response

    def invite_members(self, emails: List[str]) -> APIResponse:
        """
        批量邀请成员

        Args:
            emails: 邮箱列表

        Returns:
            API响应对象
        """
        if not emails:
            return APIResponse.error_response("邮箱列表不能为空")

        # 验证邮箱
        validation_result = EmailValidator.validate_email_list(emails)
        valid_emails = validation_result['valid']
        invalid_emails = validation_result['invalid']
        duplicates = validation_result['duplicates']

        if invalid_emails:
            self.logger.warning("邮箱验证", f"发现 {len(invalid_emails)} 个无效邮箱")

        if duplicates:
            self.logger.warning("邮箱验证", f"发现 {len(duplicates)} 个重复邮箱")

        if not valid_emails:
            return APIResponse.error_response("没有有效的邮箱地址")

        self.logger.info("邀请成员", f"准备邀请 {len(valid_emails)} 个有效邮箱")

        # 发起请求
        data = {"emails": valid_emails}
        response = self._make_request("POST", API_ENDPOINTS['INVITE'], json=data)

        # 清除相关缓存
        if response.success:
            self._clear_team_cache()
            self.logger.success("邀请成功", f"成功邀请 {len(valid_emails)} 个成员")

        return response

    def delete_member(self, member_id: str) -> APIResponse:
        """
        删除单个成员或邀请

        Args:
            member_id: 成员ID

        Returns:
            API响应对象
        """
        if not member_id:
            return APIResponse.error_response("成员ID不能为空")

        # 构建删除端点
        endpoint = API_ENDPOINTS['DELETE_MEMBER'].format(member_id=member_id)

        # 发起请求
        response = self._make_request("DELETE", endpoint)

        # 清除相关缓存
        if response.success:
            self._clear_team_cache()
            self.logger.success("删除成功", f"成功删除成员: {member_id}")

        return response

    def batch_delete_members(self, member_ids: List[str]) -> Dict[str, Any]:
        """
        批量删除成员

        Args:
            member_ids: 成员ID列表

        Returns:
            批量操作结果
        """
        if not member_ids:
            return {
                'success': False,
                'message': '成员ID列表不能为空',
                'results': []
            }

        results = []
        success_count = 0
        failed_count = 0

        self.logger.info("批量删除", f"开始批量删除 {len(member_ids)} 个成员")

        for i, member_id in enumerate(member_ids):
            self.logger.debug("删除进度", f"删除成员 {i + 1}/{len(member_ids)}: {member_id}")

            response = self.delete_member(member_id)

            result = {
                'member_id': member_id,
                'success': response.success,
                'message': response.message
            }
            results.append(result)

            if response.success:
                success_count += 1
            else:
                failed_count += 1

        # 汇总结果
        summary = {
            'success': failed_count == 0,
            'message': f"批量删除完成：成功 {success_count} 个，失败 {failed_count} 个",
            'results': results,
            'statistics': {
                'total': len(member_ids),
                'success_count': success_count,
                'failed_count': failed_count,
                'success_rate': success_count / len(member_ids) * 100
            }
        }

        self.logger.info("批量删除完成", summary['message'])
        return summary

    def put_user_on_plan(self, plan_id: str) -> APIResponse:
        """
        将用户切换到指定计划

        Args:
            plan_id: 计划ID

        Returns:
            API响应对象
        """
        if not plan_id:
            return APIResponse.error_response("计划ID不能为空")

        # 验证计划ID
        valid_plans = list(PLAN_IDS.values())
        if plan_id not in valid_plans:
            self.logger.warning("计划验证", f"未知的计划ID: {plan_id}")

        data = {"planId": plan_id}
        response = self._make_request("POST", API_ENDPOINTS['PUT_USER_ON_PLAN'], json=data)

        if response.success:
            self.logger.success("计划切换", f"成功切换到计划: {plan_id}")

        return response

    def put_user_on_community_plan(self) -> APIResponse:
        """将用户切换到社区计划"""
        return self.put_user_on_plan(PLAN_IDS['COMMUNITY'])

    def put_user_on_max_plan(self) -> APIResponse:
        """将用户切换到最大计划"""
        return self.put_user_on_plan(PLAN_IDS['MAX'])

    def _clear_team_cache(self):
        """清除团队相关缓存"""
        cache_keys_to_remove = []
        for key in self.cache.keys():
            if API_ENDPOINTS['TEAM'] in key:
                cache_keys_to_remove.append(key)

        for key in cache_keys_to_remove:
            del self.cache[key]

        if cache_keys_to_remove:
            self.logger.debug("缓存清理", f"清除了 {len(cache_keys_to_remove)} 个缓存项")

    def clear_all_cache(self):
        """清除所有缓存"""
        cache_count = len(self.cache)
        self.cache.clear()
        self.logger.info("缓存清理", f"已清除所有缓存 ({cache_count} 项)")

    def get_request_statistics(self) -> Dict[str, Any]:
        """
        获取请求统计信息

        Returns:
            统计信息字典
        """
        stats = self.request_stats.copy()

        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = (stats['successful_requests'] / stats['total_requests']) * 100
            stats['cache_hit_rate'] = (stats['cache_hits'] / stats['total_requests']) * 100
        else:
            stats['success_rate'] = 0
            stats['cache_hit_rate'] = 0

        # 格式化时间
        if stats['last_request_time']:
            stats['last_request_time_formatted'] = stats['last_request_time'].strftime('%Y-%m-%d %H:%M:%S')

        return stats

    def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            健康状态信息
        """
        health_info = {
            'status': 'unknown',
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }

        try:
            # 检查配置
            base_url = self.config.get('api.base_url')
            if not base_url:
                health_info['checks']['config'] = {'status': 'fail', 'message': 'API基础URL未配置'}
            else:
                health_info['checks']['config'] = {'status': 'pass', 'message': 'API配置正常'}

            # 检查网络连接
            try:
                response = self.get_team_data(use_cache=False)
                if response.success:
                    health_info['checks']['connectivity'] = {'status': 'pass', 'message': 'API连接正常'}
                    health_info['status'] = 'healthy'
                else:
                    health_info['checks']['connectivity'] = {'status': 'fail', 'message': f'API连接失败: {response.message}'}
                    health_info['status'] = 'unhealthy'
            except Exception as e:
                health_info['checks']['connectivity'] = {'status': 'fail', 'message': f'连接检查异常: {str(e)}'}
                health_info['status'] = 'unhealthy'

            # 检查缓存
            health_info['checks']['cache'] = {
                'status': 'pass',
                'message': f'缓存正常 ({len(self.cache)} 项)',
                'cache_size': len(self.cache),
                'cache_maxsize': self.cache.maxsize
            }

        except Exception as e:
            health_info['status'] = 'error'
            health_info['error'] = str(e)

        return health_info

    def close(self):
        """关闭客户端，清理资源"""
        if self.session:
            self.session.close()

        if self._async_session and not self._async_session.closed:
            asyncio.create_task(self._async_session.close())

        self.clear_all_cache()
        self.logger.info("API客户端", "API客户端已关闭")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
