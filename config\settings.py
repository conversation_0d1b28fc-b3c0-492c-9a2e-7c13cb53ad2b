# -*- coding: utf-8 -*-
"""
配置管理模块
提供应用程序配置的加载、保存和管理功能
"""

import os
import json
from typing import Dict, Any, Optional
from .constants import DEFAULT_SETTINGS, FILE_PATHS


class Config:
    """增强版配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认使用常量中定义的路径
        """
        self.config_file = config_file or FILE_PATHS['CONFIG']
        self.default_config = DEFAULT_SETTINGS.copy()
        self.config = self.load_config()
        self._observers = []  # 配置变更观察者
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                merged_config = self._merge_config(self.default_config, config)
                print(f"✅ 配置文件加载成功: {self.config_file}")
                return merged_config
            except Exception as e:
                print(f"❌ 加载配置文件失败: {e}")
                print(f"🔄 使用默认配置")
                return self.default_config.copy()
        else:
            print(f"📝 配置文件不存在，创建默认配置: {self.config_file}")
            # 创建默认配置文件
            self.save_config()
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            保存是否成功
        """
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置保存成功: {self.config_file}")
            self._notify_observers('config_saved')
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """
        递归合并配置
        
        Args:
            default: 默认配置
            user: 用户配置
            
        Returns:
            合并后的配置
        """
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, path: str, default=None):
        """
        获取配置值，支持点分隔路径
        
        Args:
            path: 配置路径，如 'api.base_url'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value
    
    def set(self, path: str, value: Any, save: bool = False):
        """
        设置配置值，支持点分隔路径
        
        Args:
            path: 配置路径，如 'api.base_url'
            value: 配置值
            save: 是否立即保存到文件
        """
        keys = path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        old_value = config.get(keys[-1])
        config[keys[-1]] = value
        
        # 通知观察者配置变更
        self._notify_observers('config_changed', {
            'path': path,
            'old_value': old_value,
            'new_value': value
        })
        
        if save:
            self.save_config()
    
    def reset_to_default(self, save: bool = True):
        """
        重置为默认配置
        
        Args:
            save: 是否立即保存到文件
        """
        self.config = self.default_config.copy()
        if save:
            self.save_config()
        self._notify_observers('config_reset')
        print("🔄 配置已重置为默认值")
    
    def update_config(self, new_config: Dict[str, Any], save: bool = True):
        """
        批量更新配置
        
        Args:
            new_config: 新配置字典
            save: 是否立即保存到文件
        """
        self.config = self._merge_config(self.config, new_config)
        if save:
            self.save_config()
        self._notify_observers('config_updated', new_config)
    
    def export_config(self, file_path: str) -> bool:
        """
        导出配置到指定文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置导出成功: {file_path}")
            return True
        except Exception as e:
            print(f"❌ 配置导出失败: {e}")
            return False
    
    def import_config(self, file_path: str, merge: bool = True) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 导入文件路径
            merge: 是否与现有配置合并
            
        Returns:
            导入是否成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            if merge:
                self.config = self._merge_config(self.config, imported_config)
            else:
                self.config = imported_config
            
            self.save_config()
            self._notify_observers('config_imported', imported_config)
            print(f"✅ 配置导入成功: {file_path}")
            return True
        except Exception as e:
            print(f"❌ 配置导入失败: {e}")
            return False
    
    def add_observer(self, observer):
        """
        添加配置变更观察者
        
        Args:
            observer: 观察者对象，需要有 on_config_change 方法
        """
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer):
        """
        移除配置变更观察者
        
        Args:
            observer: 观察者对象
        """
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_observers(self, event_type: str, data: Any = None):
        """
        通知所有观察者配置变更
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        for observer in self._observers:
            if hasattr(observer, 'on_config_change'):
                try:
                    observer.on_config_change(event_type, data)
                except Exception as e:
                    print(f"❌ 通知观察者失败: {e}")
    
    def validate_config(self) -> Dict[str, list]:
        """
        验证配置的有效性
        
        Returns:
            验证结果字典，包含错误和警告
        """
        errors = []
        warnings = []
        
        # 验证API配置
        base_url = self.get('api.base_url', '')
        if not base_url:
            errors.append("API基础URL不能为空")
        elif not base_url.startswith(('http://', 'https://')):
            errors.append("API基础URL格式无效")
        
        # 验证Cookie
        cookie = self.get('api.headers.cookie', '')
        if not cookie:
            warnings.append("Cookie为空，可能无法正常访问API")
        elif '_session=' not in cookie:
            warnings.append("Cookie中缺少_session字段")
        
        # 验证UI配置
        font_size = self.get('ui.font_size', 10)
        if not isinstance(font_size, int) or font_size < 8 or font_size > 20:
            warnings.append("字体大小应在8-20之间")
        
        opacity = self.get('ui.opacity', 100)
        if not isinstance(opacity, (int, float)) or opacity < 10 or opacity > 100:
            warnings.append("透明度应在10-100之间")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'is_valid': len(errors) == 0
        }
    
    def get_config_summary(self) -> str:
        """
        获取配置摘要信息
        
        Returns:
            配置摘要字符串
        """
        summary = []
        summary.append(f"📁 配置文件: {self.config_file}")
        summary.append(f"🌐 API地址: {self.get('api.base_url', 'N/A')}")
        summary.append(f"🎨 UI主题: {self.get('ui.theme', 'N/A')}")
        summary.append(f"📝 字体大小: {self.get('ui.font_size', 'N/A')}")
        summary.append(f"🔄 自动刷新: {'启用' if self.get('ui.auto_refresh', False) else '禁用'}")
        summary.append(f"🔒 Cookie加密: {'启用' if self.get('security.encrypt_cookies', False) else '禁用'}")
        
        validation = self.validate_config()
        if validation['errors']:
            summary.append(f"❌ 配置错误: {len(validation['errors'])} 个")
        if validation['warnings']:
            summary.append(f"⚠️ 配置警告: {len(validation['warnings'])} 个")
        
        return '\n'.join(summary)
