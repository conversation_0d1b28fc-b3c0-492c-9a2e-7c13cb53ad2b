# -*- coding: utf-8 -*-
"""
确认对话框组件
提供现代化的确认操作界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ui.styles import StyleManager


class CustomConfirmDialog(QDialog):
    """自定义确认对话框"""
    
    # 自定义信号
    confirmed = pyqtSignal()
    cancelled = pyqtSignal()
    
    def __init__(self, title: str, message: str, confirm_text: str = "确定", 
                 cancel_text: str = "取消", danger: bool = False, parent=None):
        """
        初始化确认对话框
        
        Args:
            title: 对话框标题
            message: 确认消息
            confirm_text: 确认按钮文本
            cancel_text: 取消按钮文本
            danger: 是否为危险操作
            parent: 父组件
        """
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        self.setModal(True)
        
        self.result_value = False
        self.danger = danger
        
        self.init_ui(title, message, confirm_text, cancel_text)
    
    def init_ui(self, title: str, message: str, confirm_text: str, cancel_text: str):
        """
        初始化对话框界面
        
        Args:
            title: 标题
            message: 消息内容
            confirm_text: 确认按钮文本
            cancel_text: 取消按钮文本
        """
        layout = QVBoxLayout()
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)
        
        # 标题区域
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        # 图标
        icon = "⚠️" if self.danger else "❓"
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 28px;")
        
        # 标题文本
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        color = StyleManager.get_color('DANGER') if self.danger else StyleManager.get_color('PRIMARY')
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                margin-left: 10px;
            }}
        """)
        
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addWidget(title_frame)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet(f"background-color: {StyleManager.get_color('NEUTRAL_MEDIUM')};")
        layout.addWidget(separator)
        
        # 消息内容
        msg_label = QLabel(message)
        msg_label.setWordWrap(True)
        msg_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        msg_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                line-height: 1.6;
                color: {StyleManager.get_color('TEXT')};
                padding: 15px;
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                border-radius: 8px;
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
            }}
        """)
        msg_label.setMinimumWidth(400)
        msg_label.setMaximumWidth(600)
        layout.addWidget(msg_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 取消按钮
        cancel_btn = StyleManager.create_button(cancel_text)
        cancel_btn.clicked.connect(self.reject)
        
        # 确认按钮
        confirm_btn_class = "danger" if self.danger else "primary"
        confirm_btn = StyleManager.create_button(confirm_text, confirm_btn_class)
        confirm_btn.clicked.connect(self.accept)
        
        # 设置默认按钮
        if not self.danger:
            confirm_btn.setDefault(True)
        else:
            cancel_btn.setDefault(True)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(confirm_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 调整大小
        self.adjustSize()
        self.setMinimumSize(500, 250)
        
        # 应用样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {StyleManager.get_color('BACKGROUND')};
                border-radius: 12px;
            }}
        """)
        
        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))
    
    def accept(self):
        """确认操作"""
        self.result_value = True
        self.confirmed.emit()
        super().accept()
    
    def reject(self):
        """取消操作"""
        self.result_value = False
        self.cancelled.emit()
        super().reject()
    
    def get_result(self) -> bool:
        """
        获取对话框结果
        
        Returns:
            用户是否确认
        """
        return self.result_value
    
    @staticmethod
    def ask_confirmation(parent, title: str, message: str, 
                        confirm_text: str = "确定", cancel_text: str = "取消", 
                        danger: bool = False) -> bool:
        """
        显示确认对话框并返回结果
        
        Args:
            parent: 父组件
            title: 对话框标题
            message: 确认消息
            confirm_text: 确认按钮文本
            cancel_text: 取消按钮文本
            danger: 是否为危险操作
            
        Returns:
            用户是否确认
        """
        dialog = CustomConfirmDialog(title, message, confirm_text, cancel_text, danger, parent)
        result = dialog.exec()
        return result == QDialog.DialogCode.Accepted
    
    @staticmethod
    def ask_delete_confirmation(parent, item_name: str = "此项目") -> bool:
        """
        显示删除确认对话框
        
        Args:
            parent: 父组件
            item_name: 要删除的项目名称
            
        Returns:
            用户是否确认删除
        """
        title = "确认删除"
        message = f"您确定要删除 {item_name} 吗？\n\n此操作无法撤销，请谨慎操作。"
        return CustomConfirmDialog.ask_confirmation(
            parent, title, message, "删除", "取消", danger=True
        )
    
    @staticmethod
    def ask_batch_delete_confirmation(parent, count: int) -> bool:
        """
        显示批量删除确认对话框
        
        Args:
            parent: 父组件
            count: 要删除的项目数量
            
        Returns:
            用户是否确认删除
        """
        title = "确认批量删除"
        message = f"您确定要删除选中的 {count} 个项目吗？\n\n此操作无法撤销，请谨慎操作。"
        return CustomConfirmDialog.ask_confirmation(
            parent, title, message, f"删除 {count} 项", "取消", danger=True
        )
    
    @staticmethod
    def ask_save_confirmation(parent, file_name: str = "当前文件") -> bool:
        """
        显示保存确认对话框
        
        Args:
            parent: 父组件
            file_name: 文件名称
            
        Returns:
            用户是否确认保存
        """
        title = "确认保存"
        message = f"是否保存对 {file_name} 的更改？\n\n如果不保存，您的更改将会丢失。"
        return CustomConfirmDialog.ask_confirmation(
            parent, title, message, "保存", "不保存"
        )
    
    @staticmethod
    def ask_exit_confirmation(parent) -> bool:
        """
        显示退出确认对话框
        
        Args:
            parent: 父组件
            
        Returns:
            用户是否确认退出
        """
        title = "确认退出"
        message = "您确定要退出团队管理工具吗？\n\n未保存的更改可能会丢失。"
        return CustomConfirmDialog.ask_confirmation(
            parent, title, message, "退出", "取消"
        )
