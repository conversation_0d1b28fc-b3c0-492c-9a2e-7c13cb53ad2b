# -*- coding: utf-8 -*-
"""
配置对话框组件
提供配置管理的用户界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QPushButton, QTextEdit, QCheckBox,
    QSpinBox, QComboBox, QGroupBox, QFormLayout, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ui.styles import StyleManager


class ConfigDialog(QDialog):
    """配置对话框"""
    
    # 信号定义
    config_saved = pyqtSignal(dict)  # 配置保存信号
    
    def __init__(self, config, parent=None):
        """
        初始化配置对话框
        
        Args:
            config: 配置对象
            parent: 父组件
        """
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("配置设置")
        self.setModal(True)
        self.resize(600, 500)
        
        self.init_ui()
        self.load_config_values()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("⚙️ 配置设置")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleManager.get_color('PRIMARY')};")
        
        # 选项卡
        self.tab_widget = QTabWidget()
        
        # API配置选项卡
        self.create_api_tab()
        
        # UI配置选项卡
        self.create_ui_tab()
        
        # 安全配置选项卡
        self.create_security_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 测试连接按钮
        test_btn = StyleManager.create_button("🔍 测试连接", "info")
        test_btn.clicked.connect(self.test_connection)
        
        # 重置按钮
        reset_btn = StyleManager.create_button("🔄 重置", "warning")
        reset_btn.clicked.connect(self.reset_config)
        
        # 取消按钮
        cancel_btn = StyleManager.create_button("❌ 取消", "")
        cancel_btn.clicked.connect(self.reject)
        
        # 保存按钮
        save_btn = StyleManager.create_button("💾 保存", "primary")
        save_btn.clicked.connect(self.save_config)
        save_btn.setDefault(True)
        
        button_layout.addWidget(test_btn)
        button_layout.addWidget(reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        
        layout.addWidget(title_label)
        layout.addWidget(self.tab_widget)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 应用样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {StyleManager.get_color('BACKGROUND')};
            }}
        """)
        
        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))
    
    def create_api_tab(self):
        """创建API配置选项卡"""
        api_widget = QWidget()
        layout = QVBoxLayout(api_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # API基础设置
        api_group = QGroupBox("API设置")
        api_form = QFormLayout(api_group)
        
        # 基础URL
        self.base_url_input = QLineEdit()
        self.base_url_input.setPlaceholderText("https://app.augmentcode.com/api")
        api_form.addRow("基础URL:", self.base_url_input)
        
        # Cookie设置
        cookie_group = QGroupBox("认证设置")
        cookie_layout = QVBoxLayout(cookie_group)
        
        cookie_label = QLabel("Cookie:")
        self.cookie_input = QTextEdit()
        self.cookie_input.setMaximumHeight(100)
        self.cookie_input.setPlaceholderText("请输入完整的Cookie字符串...")
        
        cookie_layout.addWidget(cookie_label)
        cookie_layout.addWidget(self.cookie_input)
        
        # 超时设置
        timeout_group = QGroupBox("请求设置")
        timeout_form = QFormLayout(timeout_group)
        
        self.timeout_input = QSpinBox()
        self.timeout_input.setRange(5, 120)
        self.timeout_input.setSuffix(" 秒")
        timeout_form.addRow("请求超时:", self.timeout_input)
        
        layout.addWidget(api_group)
        layout.addWidget(cookie_group)
        layout.addWidget(timeout_group)
        layout.addStretch()
        
        self.tab_widget.addTab(api_widget, "🌐 API配置")
    
    def create_ui_tab(self):
        """创建UI配置选项卡"""
        ui_widget = QWidget()
        layout = QVBoxLayout(ui_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_form = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["light", "dark"])
        theme_form.addRow("主题:", self.theme_combo)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_form = QFormLayout(font_group)
        
        self.font_size_input = QSpinBox()
        self.font_size_input.setRange(8, 20)
        font_form.addRow("字体大小:", self.font_size_input)
        
        # 界面设置
        interface_group = QGroupBox("界面设置")
        interface_layout = QVBoxLayout(interface_group)
        
        self.auto_refresh_check = QCheckBox("启用自动刷新")
        self.animations_check = QCheckBox("启用动画效果")
        self.shadows_check = QCheckBox("启用阴影效果")
        
        interface_layout.addWidget(self.auto_refresh_check)
        interface_layout.addWidget(self.animations_check)
        interface_layout.addWidget(self.shadows_check)
        
        layout.addWidget(theme_group)
        layout.addWidget(font_group)
        layout.addWidget(interface_group)
        layout.addStretch()
        
        self.tab_widget.addTab(ui_widget, "🎨 界面配置")
    
    def create_security_tab(self):
        """创建安全配置选项卡"""
        security_widget = QWidget()
        layout = QVBoxLayout(security_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 安全设置
        security_group = QGroupBox("安全设置")
        security_layout = QVBoxLayout(security_group)
        
        self.encrypt_cookies_check = QCheckBox("加密存储Cookie")
        self.encrypt_config_check = QCheckBox("加密配置文件")
        self.auto_logout_check = QCheckBox("自动登出")
        
        security_layout.addWidget(self.encrypt_cookies_check)
        security_layout.addWidget(self.encrypt_config_check)
        security_layout.addWidget(self.auto_logout_check)
        
        # 数据设置
        data_group = QGroupBox("数据设置")
        data_layout = QVBoxLayout(data_group)
        
        self.cache_enabled_check = QCheckBox("启用数据缓存")
        self.auto_save_check = QCheckBox("自动保存配置")
        
        data_layout.addWidget(self.cache_enabled_check)
        data_layout.addWidget(self.auto_save_check)
        
        layout.addWidget(security_group)
        layout.addWidget(data_group)
        layout.addStretch()
        
        self.tab_widget.addTab(security_widget, "🔒 安全配置")
    
    def load_config_values(self):
        """加载配置值到界面"""
        # API配置
        self.base_url_input.setText(self.config.get('api.base_url', ''))
        cookie = self.config.get('api.headers.cookie', '')
        self.cookie_input.setPlainText(cookie)
        self.timeout_input.setValue(self.config.get('api.timeout', 30))
        
        # UI配置
        self.theme_combo.setCurrentText(self.config.get('ui.theme', 'light'))
        self.font_size_input.setValue(self.config.get('ui.font_size', 10))
        self.auto_refresh_check.setChecked(self.config.get('ui.auto_refresh', True))
        self.animations_check.setChecked(self.config.get('ui.animations', True))
        self.shadows_check.setChecked(self.config.get('ui.shadows', True))
        
        # 安全配置
        self.encrypt_cookies_check.setChecked(self.config.get('security.encrypt_cookies', True))
        self.encrypt_config_check.setChecked(self.config.get('security.encrypt_config', False))
        self.auto_logout_check.setChecked(self.config.get('security.auto_logout', False))
        self.cache_enabled_check.setChecked(self.config.get('features.cache_enabled', True))
        self.auto_save_check.setChecked(self.config.get('features.auto_save', True))
    
    def save_config(self):
        """保存配置"""
        # 收集配置值
        config_data = {
            'api': {
                'base_url': self.base_url_input.text().strip(),
                'headers': {
                    'cookie': self.cookie_input.toPlainText().strip()
                },
                'timeout': self.timeout_input.value()
            },
            'ui': {
                'theme': self.theme_combo.currentText(),
                'font_size': self.font_size_input.value(),
                'auto_refresh': self.auto_refresh_check.isChecked(),
                'animations': self.animations_check.isChecked(),
                'shadows': self.shadows_check.isChecked()
            },
            'security': {
                'encrypt_cookies': self.encrypt_cookies_check.isChecked(),
                'encrypt_config': self.encrypt_config_check.isChecked(),
                'auto_logout': self.auto_logout_check.isChecked()
            },
            'features': {
                'cache_enabled': self.cache_enabled_check.isChecked(),
                'auto_save': self.auto_save_check.isChecked()
            }
        }
        
        # 更新配置对象
        for section, values in config_data.items():
            for key, value in values.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        self.config.set(f'{section}.{key}.{sub_key}', sub_value)
                else:
                    self.config.set(f'{section}.{key}', value)
        
        # 保存配置文件
        if self.config.save_config():
            self.config_saved.emit(config_data)
            self.accept()
        else:
            # 显示错误消息
            from ui.dialogs import CustomMessageBox
            CustomMessageBox.show_error(self, "保存失败", "配置保存失败，请检查文件权限。")
    
    def reset_config(self):
        """重置配置"""
        from ui.dialogs import CustomConfirmDialog
        
        if CustomConfirmDialog.ask_confirmation(
            self, "重置配置", 
            "确定要重置所有配置到默认值吗？\n\n此操作不可撤销。"
        ):
            # 重置到默认值
            self.load_default_values()
    
    def load_default_values(self):
        """加载默认值"""
        self.base_url_input.setText("https://app.augmentcode.com/api")
        self.cookie_input.clear()
        self.timeout_input.setValue(30)
        
        self.theme_combo.setCurrentText("light")
        self.font_size_input.setValue(10)
        self.auto_refresh_check.setChecked(True)
        self.animations_check.setChecked(True)
        self.shadows_check.setChecked(True)
        
        self.encrypt_cookies_check.setChecked(True)
        self.encrypt_config_check.setChecked(False)
        self.auto_logout_check.setChecked(False)
        self.cache_enabled_check.setChecked(True)
        self.auto_save_check.setChecked(True)
    
    def test_connection(self):
        """测试API连接"""
        # 这里可以添加连接测试逻辑
        from ui.dialogs import CustomMessageBox
        CustomMessageBox.show_info(self, "测试连接", "连接测试功能待实现...")
    
    @staticmethod
    def show_config_dialog(config, parent=None):
        """
        显示配置对话框
        
        Args:
            config: 配置对象
            parent: 父组件
            
        Returns:
            bool: 是否保存了配置
        """
        dialog = ConfigDialog(config, parent)
        return dialog.exec() == QDialog.DialogCode.Accepted
