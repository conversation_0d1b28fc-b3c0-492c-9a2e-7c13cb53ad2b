# -*- coding: utf-8 -*-
"""
自定义消息框组件
提供现代化的消息显示功能
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QGraphicsDropShadowEffect, QColor

from ui.styles import StyleManager


class CustomMessageBox(QWidget):
    """自定义消息框，确保内容完整显示"""

    def __init__(self, title: str, message: str, msg_type: str = "info", parent=None):
        """
        初始化消息框
        
        Args:
            title: 标题
            message: 消息内容
            msg_type: 消息类型 (info, success, warning, error)
            parent: 父组件
        """
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        self.setModal(True)
        self.init_ui(title, message, msg_type)

    def init_ui(self, title: str, message: str, msg_type: str):
        """
        初始化消息框界面
        
        Args:
            title: 标题
            message: 消息内容
            msg_type: 消息类型
        """
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 根据类型设置图标和颜色
        icon_config = self._get_icon_config(msg_type)
        
        # 标题区域
        title_layout = QHBoxLayout()
        title_icon = QLabel(icon_config['icon'])
        title_icon.setStyleSheet("font-size: 24px;")
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {icon_config['color']};
                margin-left: 10px;
            }}
        """)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        layout.addLayout(title_layout)

        # 消息内容
        msg_label = QLabel(message)
        msg_label.setWordWrap(True)
        msg_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        msg_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                line-height: 1.5;
                padding: 10px;
                background: {icon_config['bg_color']};
                border-radius: 8px;
                border: 1px solid {icon_config['border_color']};
                color: {icon_config['text_color']};
            }}
        """)
        msg_label.setMinimumWidth(400)
        msg_label.setMaximumWidth(600)
        layout.addWidget(msg_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        ok_btn = StyleManager.create_button("确定", "primary")
        ok_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 根据内容调整大小
        self.adjustSize()
        self.setMinimumSize(450, 200)

        # 应用样式和阴影
        self.setStyleSheet(f"""
            QWidget {{
                background: {StyleManager.get_color('BACKGROUND')};
                border-radius: 12px;
            }}
        """)
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))

    def _get_icon_config(self, msg_type: str) -> dict:
        """
        获取消息类型对应的图标配置
        
        Args:
            msg_type: 消息类型
            
        Returns:
            图标配置字典
        """
        configs = {
            "success": {
                "icon": "✅",
                "color": StyleManager.get_color('SUCCESS'),
                "bg_color": f"{StyleManager.get_color('SUCCESS')}15",
                "border_color": f"{StyleManager.get_color('SUCCESS')}40",
                "text_color": StyleManager.get_color('SUCCESS')
            },
            "error": {
                "icon": "❌",
                "color": StyleManager.get_color('DANGER'),
                "bg_color": f"{StyleManager.get_color('DANGER')}15",
                "border_color": f"{StyleManager.get_color('DANGER')}40",
                "text_color": StyleManager.get_color('DANGER')
            },
            "warning": {
                "icon": "⚠️",
                "color": StyleManager.get_color('WARNING'),
                "bg_color": f"{StyleManager.get_color('WARNING')}15",
                "border_color": f"{StyleManager.get_color('WARNING')}40",
                "text_color": StyleManager.get_color('WARNING')
            },
            "info": {
                "icon": "ℹ️",
                "color": StyleManager.get_color('PRIMARY'),
                "bg_color": f"{StyleManager.get_color('PRIMARY')}15",
                "border_color": f"{StyleManager.get_color('PRIMARY')}40",
                "text_color": StyleManager.get_color('PRIMARY')
            }
        }
        
        return configs.get(msg_type, configs["info"])

    def accept(self):
        """确定按钮点击事件"""
        self.close()

    @staticmethod
    def show_info(parent, title: str, message: str):
        """
        显示信息消息框
        
        Args:
            parent: 父组件
            title: 标题
            message: 消息内容
            
        Returns:
            消息框实例
        """
        dialog = CustomMessageBox(title, message, "info", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_success(parent, title: str, message: str):
        """
        显示成功消息框
        
        Args:
            parent: 父组件
            title: 标题
            message: 消息内容
            
        Returns:
            消息框实例
        """
        dialog = CustomMessageBox(title, message, "success", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_warning(parent, title: str, message: str):
        """
        显示警告消息框
        
        Args:
            parent: 父组件
            title: 标题
            message: 消息内容
            
        Returns:
            消息框实例
        """
        dialog = CustomMessageBox(title, message, "warning", parent)
        dialog.show()
        return dialog

    @staticmethod
    def show_error(parent, title: str, message: str):
        """
        显示错误消息框
        
        Args:
            parent: 父组件
            title: 标题
            message: 消息内容
            
        Returns:
            消息框实例
        """
        dialog = CustomMessageBox(title, message, "error", parent)
        dialog.show()
        return dialog


class MessageBoxManager:
    """消息框管理器"""
    
    def __init__(self):
        """初始化消息框管理器"""
        self.active_dialogs = []
    
    def show_message(self, parent, title: str, message: str, msg_type: str = "info"):
        """
        显示消息并管理对话框生命周期
        
        Args:
            parent: 父组件
            title: 标题
            message: 消息内容
            msg_type: 消息类型
            
        Returns:
            消息框实例
        """
        dialog = CustomMessageBox(title, message, msg_type, parent)
        
        # 添加到活动对话框列表
        self.active_dialogs.append(dialog)
        
        # 连接关闭信号
        dialog.finished.connect(lambda: self._remove_dialog(dialog))
        
        dialog.show()
        return dialog
    
    def _remove_dialog(self, dialog):
        """
        从活动对话框列表中移除对话框
        
        Args:
            dialog: 对话框实例
        """
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def close_all(self):
        """关闭所有活动对话框"""
        for dialog in self.active_dialogs.copy():
            dialog.close()
        self.active_dialogs.clear()
    
    def get_active_count(self) -> int:
        """
        获取活动对话框数量
        
        Returns:
            活动对话框数量
        """
        return len(self.active_dialogs)


# 全局消息框管理器实例
message_manager = MessageBoxManager()
