# -*- coding: utf-8 -*-
"""
统计显示组件
提供数据统计和可视化功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QGridLayout,
    QProgressBar, QPushButton, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPainter, QPen, QBrush, QColor
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from ui.styles import StyleManager


class StatCard(QFrame):
    """统计卡片组件"""
    
    clicked = pyqtSignal(str)  # card_type
    
    def __init__(self, title: str, value: str, icon: str = "📊", 
                 color: str = None, card_type: str = "", parent=None):
        """
        初始化统计卡片
        
        Args:
            title: 标题
            value: 数值
            icon: 图标
            color: 主题色
            card_type: 卡片类型
            parent: 父组件
        """
        super().__init__(parent)
        self.card_type = card_type
        self.title = title
        self.current_value = value
        self.target_value = value
        self.icon = icon
        self.color = color or StyleManager.get_color('PRIMARY')
        
        self.init_ui()
        self.setup_animation()
    
    def init_ui(self):
        """初始化界面"""
        self.setFixedHeight(80)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(12)
        
        # 图标区域
        icon_label = QLabel(self.icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {self.color};
                background: {self.color}15;
                border-radius: 20px;
                min-width: 40px;
                min-height: 40px;
                padding: 5px;
            }}
        """)
        
        # 内容区域
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(2)
        
        # 数值标签
        self.value_label = QLabel(self.current_value)
        value_font = QFont()
        value_font.setPointSize(18)
        value_font.setBold(True)
        self.value_label.setFont(value_font)
        self.value_label.setStyleSheet(f"""
            QLabel {{
                color: {self.color};
                margin: 0px;
            }}
        """)
        
        # 标题标签
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setPointSize(10)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.get_color('NEUTRAL_DARK')};
                margin: 0px;
            }}
        """)
        
        content_layout.addWidget(self.value_label)
        content_layout.addWidget(title_label)
        content_layout.addStretch()
        
        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        layout.addStretch()
        
        self.setLayout(layout)
        
        # 应用卡片样式
        self.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.get_color('BACKGROUND')};
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
                border-left: 4px solid {self.color};
            }}
            
            QFrame:hover {{
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                border-color: {self.color};
            }}
        """)
        
        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=8, offset=(0, 2))
    
    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def update_value(self, new_value: str, animate: bool = True):
        """
        更新数值
        
        Args:
            new_value: 新数值
            animate: 是否使用动画
        """
        if animate and new_value.isdigit() and self.current_value.isdigit():
            # 数字动画
            self.animate_number(int(self.current_value), int(new_value))
        else:
            # 直接更新
            self.current_value = new_value
            self.value_label.setText(new_value)
    
    def animate_number(self, start: int, end: int, duration: int = 1000):
        """
        数字动画
        
        Args:
            start: 起始值
            end: 结束值
            duration: 动画时长（毫秒）
        """
        self.start_value = start
        self.end_value = end
        self.animation_steps = 30
        self.current_step = 0
        
        self.number_timer = QTimer()
        self.number_timer.timeout.connect(self.update_number_animation)
        self.number_timer.start(duration // self.animation_steps)
    
    def update_number_animation(self):
        """更新数字动画"""
        if self.current_step >= self.animation_steps:
            self.number_timer.stop()
            self.value_label.setText(str(self.end_value))
            self.current_value = str(self.end_value)
            return
        
        # 计算当前值
        progress = self.current_step / self.animation_steps
        current_val = int(self.start_value + (self.end_value - self.start_value) * progress)
        
        self.value_label.setText(str(current_val))
        self.current_step += 1
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.card_type)
        super().mousePressEvent(event)


class ProgressCard(QFrame):
    """进度卡片组件"""
    
    def __init__(self, title: str, current: int, total: int, 
                 color: str = None, parent=None):
        """
        初始化进度卡片
        
        Args:
            title: 标题
            current: 当前值
            total: 总值
            color: 主题色
            parent: 父组件
        """
        super().__init__(parent)
        self.title = title
        self.current = current
        self.total = total
        self.color = color or StyleManager.get_color('PRIMARY')
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFixedHeight(100)
        self.setFrameStyle(QFrame.Shape.StyledPanel)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)
        
        # 标题和百分比
        header_layout = QHBoxLayout()
        
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleManager.get_color('TEXT')};")
        
        percentage = (self.current / self.total * 100) if self.total > 0 else 0
        percent_label = QLabel(f"{percentage:.1f}%")
        percent_label.setStyleSheet(f"color: {self.color}; font-weight: bold;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(percent_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(self.total)
        self.progress_bar.setValue(self.current)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(8)
        
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 4px;
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
            }}
            
            QProgressBar::chunk {{
                background: {self.color};
                border-radius: 4px;
            }}
        """)
        
        # 数值标签
        value_label = QLabel(f"{self.current} / {self.total}")
        value_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')}; font-size: 12px;")
        
        layout.addLayout(header_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(value_label)
        
        self.setLayout(layout)
        
        # 应用样式
        self.setStyleSheet(f"""
            QFrame {{
                background: {StyleManager.get_color('BACKGROUND')};
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 8px;
            }}
        """)
        
        StyleManager.apply_shadow_effect(self, blur_radius=6, offset=(0, 2))
    
    def update_progress(self, current: int, total: int = None):
        """
        更新进度
        
        Args:
            current: 当前值
            total: 总值
        """
        self.current = current
        if total is not None:
            self.total = total
        
        self.progress_bar.setMaximum(self.total)
        self.progress_bar.setValue(self.current)
        
        # 更新标签
        percentage = (self.current / self.total * 100) if self.total > 0 else 0
        # 这里需要重新获取标签并更新，简化处理
        self.init_ui()


class StatsWidget(QWidget):
    """统计显示组件"""
    
    # 信号定义
    card_clicked = pyqtSignal(str, dict)  # card_type, data
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化统计组件"""
        super().__init__(parent)
        
        self.stat_cards: Dict[str, StatCard] = {}
        self.progress_cards: Dict[str, ProgressCard] = {}
        self.data_cache: Dict[str, Any] = {}
        
        self.init_ui()
        
        # 自动刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_requested.emit)
        self.refresh_timer.start(30000)  # 30秒自动刷新
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # 标题栏
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📊 数据统计")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleManager.get_color('PRIMARY')};")
        
        # 刷新按钮
        refresh_btn = StyleManager.create_button("🔄 刷新", "")
        refresh_btn.clicked.connect(self.refresh_requested.emit)
        
        # 最后更新时间
        self.last_update_label = QLabel("最后更新: 从未")
        self.last_update_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')};")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.last_update_label)
        header_layout.addWidget(refresh_btn)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 统计内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(15)
        
        # 统计卡片网格
        self.cards_grid = QGridLayout()
        self.cards_grid.setSpacing(10)
        
        # 进度卡片区域
        self.progress_layout = QVBoxLayout()
        self.progress_layout.setSpacing(10)
        
        self.content_layout.addLayout(self.cards_grid)
        self.content_layout.addLayout(self.progress_layout)
        self.content_layout.addStretch()
        
        scroll_area.setWidget(self.content_widget)
        
        layout.addLayout(header_layout)
        layout.addWidget(scroll_area)
        
        self.setLayout(layout)
    
    def add_stat_card(self, card_type: str, title: str, value: str, 
                     icon: str = "📊", color: str = None, row: int = 0, col: int = 0):
        """
        添加统计卡片
        
        Args:
            card_type: 卡片类型
            title: 标题
            value: 数值
            icon: 图标
            color: 颜色
            row: 行位置
            col: 列位置
        """
        if card_type in self.stat_cards:
            # 更新现有卡片
            self.stat_cards[card_type].update_value(value)
            return
        
        card = StatCard(title, value, icon, color, card_type)
        card.clicked.connect(lambda ct: self.card_clicked.emit(ct, self.data_cache.get(ct, {})))
        
        self.stat_cards[card_type] = card
        self.cards_grid.addWidget(card, row, col)
    
    def add_progress_card(self, card_type: str, title: str, current: int, 
                         total: int, color: str = None):
        """
        添加进度卡片
        
        Args:
            card_type: 卡片类型
            title: 标题
            current: 当前值
            total: 总值
            color: 颜色
        """
        if card_type in self.progress_cards:
            # 更新现有卡片
            self.progress_cards[card_type].update_progress(current, total)
            return
        
        card = ProgressCard(title, current, total, color)
        self.progress_cards[card_type] = card
        self.progress_layout.addWidget(card)
    
    def update_stat_card(self, card_type: str, value: str, animate: bool = True):
        """
        更新统计卡片
        
        Args:
            card_type: 卡片类型
            value: 新数值
            animate: 是否使用动画
        """
        if card_type in self.stat_cards:
            self.stat_cards[card_type].update_value(value, animate)
    
    def update_progress_card(self, card_type: str, current: int, total: int = None):
        """
        更新进度卡片
        
        Args:
            card_type: 卡片类型
            current: 当前值
            total: 总值
        """
        if card_type in self.progress_cards:
            self.progress_cards[card_type].update_progress(current, total)
    
    def set_data_cache(self, card_type: str, data: Any):
        """
        设置数据缓存
        
        Args:
            card_type: 卡片类型
            data: 数据
        """
        self.data_cache[card_type] = data
    
    def update_last_update_time(self):
        """更新最后更新时间"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_update_label.setText(f"最后更新: {current_time}")
    
    def clear_all_cards(self):
        """清除所有卡片"""
        # 清除统计卡片
        for card in self.stat_cards.values():
            card.deleteLater()
        self.stat_cards.clear()
        
        # 清除进度卡片
        for card in self.progress_cards.values():
            card.deleteLater()
        self.progress_cards.clear()
        
        # 清除数据缓存
        self.data_cache.clear()
    
    def get_statistics_summary(self) -> Dict[str, Any]:
        """
        获取统计摘要
        
        Returns:
            统计摘要字典
        """
        return {
            'stat_cards_count': len(self.stat_cards),
            'progress_cards_count': len(self.progress_cards),
            'data_cache_size': len(self.data_cache),
            'last_update': self.last_update_label.text(),
            'auto_refresh_enabled': self.refresh_timer.isActive()
        }
