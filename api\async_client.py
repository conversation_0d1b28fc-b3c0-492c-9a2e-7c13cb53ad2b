# -*- coding: utf-8 -*-
"""
异步API客户端
提供高性能的异步API调用功能
"""

import asyncio
import aiohttp
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from config.settings import Config
from config.constants import API_ENDPOINTS, PLAN_IDS
from utils.helpers import EmailValidator, URLHelper
from utils.logger import Logger
from utils.security import SecurityManager
from .models import TeamData, APIResponse


class AsyncAPIClient:
    """异步API客户端"""
    
    def __init__(self, config: Config, logger: Optional[Logger] = None,
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化异步API客户端
        
        Args:
            config: 配置管理器
            logger: 日志管理器
            security_manager: 安全管理器
        """
        self.config = config
        self.logger = logger or Logger("AsyncAPIClient")
        self.security_manager = security_manager or SecurityManager()
        
        # 异步会话（延迟初始化）
        self._session = None
        self._connector = None
        
        # 请求统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'last_request_time': None
        }
        
        self.logger.info("异步API客户端", "异步API客户端初始化完成")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """
        获取或创建异步会话
        
        Returns:
            aiohttp客户端会话
        """
        if self._session is None or self._session.closed:
            # 创建连接器
            self._connector = aiohttp.TCPConnector(
                limit=100,  # 总连接池大小
                limit_per_host=30,  # 每个主机的连接数
                ttl_dns_cache=300,  # DNS缓存TTL
                use_dns_cache=True,
            )
            
            # 设置超时
            timeout = aiohttp.ClientTimeout(
                total=self.config.get('api.timeout', 30),
                connect=10,
                sock_read=20
            )
            
            # 创建会话
            self._session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=timeout,
                headers=self._get_headers()
            )
            
            self.logger.debug("异步会话", "异步HTTP会话已创建")
        
        return self._session
    
    def _get_headers(self) -> Dict[str, str]:
        """
        获取请求头
        
        Returns:
            请求头字典
        """
        headers = self.config.get('api.headers', {}).copy()
        
        # 解密Cookie（如果启用了加密）
        if 'cookie' in headers and headers['cookie']:
            if self.config.get('security.encrypt_cookies', False):
                headers['cookie'] = self.security_manager.decrypt_cookie(headers['cookie'])
        
        # 为POST请求添加content-type
        if 'content-type' not in headers:
            headers['content-type'] = 'application/json'
        
        return headers
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> APIResponse:
        """
        发起异步HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            API响应对象
        """
        base_url = self.config.get('api.base_url', API_ENDPOINTS['BASE_URL'])
        url = f"{base_url}{endpoint}"
        
        # 验证URL安全性
        is_valid, error_msg = URLHelper.validate_url(url)
        if not is_valid:
            return APIResponse.error_response(f"URL无效: {error_msg}")
        
        session = await self._get_session()
        start_time = time.time()
        
        try:
            self.logger.debug("异步请求", f"{method} {url}")
            
            async with session.request(method, url, **kwargs) as response:
                response_time = time.time() - start_time
                self._log_request(method, url, response.status, response_time)
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        return APIResponse.success_response(data, "请求成功")
                    except Exception as e:
                        return APIResponse.error_response(f"JSON解析失败: {str(e)}", 
                                                        status_code=response.status)
                else:
                    error_text = await response.text()
                    error_msg = f"请求失败，状态码: {response.status}"
                    if error_text:
                        error_msg += f"\n响应内容: {error_text[:200]}..."
                    
                    return APIResponse.error_response(error_msg, status_code=response.status)
                    
        except asyncio.TimeoutError:
            error_msg = "请求超时"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="TIMEOUT")
            
        except aiohttp.ClientError as e:
            error_msg = f"连接错误: {str(e)}"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="CONNECTION_ERROR")
            
        except Exception as e:
            error_msg = f"网络错误: {str(e)}"
            self._log_request(method, url, error=error_msg)
            return APIResponse.error_response(error_msg, error_code="UNKNOWN_ERROR")
    
    def _log_request(self, method: str, url: str, status_code: int = None, 
                    response_time: float = None, error: str = None):
        """
        记录API请求日志
        
        Args:
            method: HTTP方法
            url: 请求URL
            status_code: 响应状态码
            response_time: 响应时间
            error: 错误信息
        """
        self.request_stats['total_requests'] += 1
        self.request_stats['last_request_time'] = datetime.now()
        
        if error:
            self.request_stats['failed_requests'] += 1
            self.logger.error("异步API请求失败", f"{method} {url} - {error}")
        elif status_code and status_code >= 400:
            self.request_stats['failed_requests'] += 1
            self.logger.warning("异步API请求警告", f"{method} {url} -> {status_code}")
        else:
            self.request_stats['successful_requests'] += 1
            self.logger.info("异步API请求成功", f"{method} {url} -> {status_code}")
        
        if response_time:
            self.logger.debug("异步响应时间", f"{response_time:.2f}秒")
    
    async def get_team_data(self) -> APIResponse:
        """
        异步获取团队数据
        
        Returns:
            API响应对象
        """
        return await self._make_request("GET", API_ENDPOINTS['TEAM'])
    
    async def invite_members(self, emails: List[str]) -> APIResponse:
        """
        异步批量邀请成员
        
        Args:
            emails: 邮箱列表
            
        Returns:
            API响应对象
        """
        if not emails:
            return APIResponse.error_response("邮箱列表不能为空")
        
        # 验证邮箱
        validation_result = EmailValidator.validate_email_list(emails)
        valid_emails = validation_result['valid']
        
        if not valid_emails:
            return APIResponse.error_response("没有有效的邮箱地址")
        
        data = {"emails": valid_emails}
        return await self._make_request("POST", API_ENDPOINTS['INVITE'], json=data)
    
    async def delete_member(self, member_id: str) -> APIResponse:
        """
        异步删除单个成员
        
        Args:
            member_id: 成员ID
            
        Returns:
            API响应对象
        """
        if not member_id:
            return APIResponse.error_response("成员ID不能为空")
        
        endpoint = API_ENDPOINTS['DELETE_MEMBER'].format(member_id=member_id)
        return await self._make_request("DELETE", endpoint)
    
    async def batch_delete_members(self, member_ids: List[str], 
                                 max_concurrent: int = 5) -> Dict[str, Any]:
        """
        异步批量删除成员（并发执行）
        
        Args:
            member_ids: 成员ID列表
            max_concurrent: 最大并发数
            
        Returns:
            批量操作结果
        """
        if not member_ids:
            return {
                'success': False,
                'message': '成员ID列表不能为空',
                'results': []
            }
        
        self.logger.info("异步批量删除", f"开始并发删除 {len(member_ids)} 个成员，并发数: {max_concurrent}")
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def delete_with_semaphore(member_id: str) -> Dict[str, Any]:
            async with semaphore:
                response = await self.delete_member(member_id)
                return {
                    'member_id': member_id,
                    'success': response.success,
                    'message': response.message
                }
        
        # 并发执行删除操作
        tasks = [delete_with_semaphore(member_id) for member_id in member_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        processed_results = []
        success_count = 0
        failed_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    'member_id': 'unknown',
                    'success': False,
                    'message': f'异常: {str(result)}'
                })
                failed_count += 1
            else:
                processed_results.append(result)
                if result['success']:
                    success_count += 1
                else:
                    failed_count += 1
        
        summary = {
            'success': failed_count == 0,
            'message': f"异步批量删除完成：成功 {success_count} 个，失败 {failed_count} 个",
            'results': processed_results,
            'statistics': {
                'total': len(member_ids),
                'success_count': success_count,
                'failed_count': failed_count,
                'success_rate': success_count / len(member_ids) * 100,
                'concurrent_level': max_concurrent
            }
        }
        
        self.logger.info("异步批量删除完成", summary['message'])
        return summary
    
    async def put_user_on_plan(self, plan_id: str) -> APIResponse:
        """
        异步切换用户计划
        
        Args:
            plan_id: 计划ID
            
        Returns:
            API响应对象
        """
        if not plan_id:
            return APIResponse.error_response("计划ID不能为空")
        
        data = {"planId": plan_id}
        return await self._make_request("POST", API_ENDPOINTS['PUT_USER_ON_PLAN'], json=data)
    
    async def close(self):
        """关闭异步客户端"""
        if self._session and not self._session.closed:
            await self._session.close()
        
        if self._connector:
            await self._connector.close()
        
        self.logger.info("异步API客户端", "异步API客户端已关闭")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
