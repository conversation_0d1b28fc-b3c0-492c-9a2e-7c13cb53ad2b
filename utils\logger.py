# -*- coding: utf-8 -*-
"""
日志管理模块
提供结构化日志记录功能
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from logging.handlers import RotatingFileHandler
from config.constants import LOG_LEVELS, FILE_PATHS


class Logger:
    """增强版日志管理器"""
    
    def __init__(self, name: str = "TeamManager", log_dir: str = None):
        """
        初始化日志管理器
        
        Args:
            name: 日志器名称
            log_dir: 日志目录
        """
        self.name = name
        self.log_dir = log_dir or FILE_PATHS.get('LOGS', 'logs')
        self._ensure_log_dir()
        
        # 创建日志器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 内存日志存储（用于UI显示）
        self.memory_logs: List[Dict[str, Any]] = []
        self.max_memory_logs = 1000
    
    def _ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir, exist_ok=True)
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器 - 详细日志
        log_file = os.path.join(self.log_dir, f"{self.name.lower()}.log")
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 错误日志文件
        error_log_file = os.path.join(self.log_dir, f"{self.name.lower()}_error.log")
        error_handler = RotatingFileHandler(
            error_log_file, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(detailed_formatter)
        error_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
    
    def _add_to_memory(self, level: str, title: str, message: str, extra_data: Dict = None):
        """
        添加日志到内存存储
        
        Args:
            level: 日志级别
            title: 日志标题
            message: 日志消息
            extra_data: 额外数据
        """
        log_entry = {
            'timestamp': datetime.now(),
            'level': level,
            'title': title,
            'message': message,
            'extra_data': extra_data or {}
        }
        
        self.memory_logs.append(log_entry)
        
        # 限制内存日志数量
        if len(self.memory_logs) > self.max_memory_logs:
            self.memory_logs = self.memory_logs[-self.max_memory_logs:]
    
    def debug(self, title: str, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(f"{title}: {message}")
        self._add_to_memory("DEBUG", title, message, kwargs)
    
    def info(self, title: str, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(f"{title}: {message}")
        self._add_to_memory("INFO", title, message, kwargs)
    
    def success(self, title: str, message: str, **kwargs):
        """记录成功日志"""
        self.logger.info(f"✅ {title}: {message}")
        self._add_to_memory("SUCCESS", title, message, kwargs)
    
    def warning(self, title: str, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(f"{title}: {message}")
        self._add_to_memory("WARNING", title, message, kwargs)
    
    def error(self, title: str, message: str, exception: Exception = None, **kwargs):
        """记录错误日志"""
        if exception:
            self.logger.error(f"{title}: {message}", exc_info=exception)
            kwargs['exception'] = str(exception)
        else:
            self.logger.error(f"{title}: {message}")
        self._add_to_memory("ERROR", title, message, kwargs)
    
    def critical(self, title: str, message: str, exception: Exception = None, **kwargs):
        """记录严重错误日志"""
        if exception:
            self.logger.critical(f"{title}: {message}", exc_info=exception)
            kwargs['exception'] = str(exception)
        else:
            self.logger.critical(f"{title}: {message}")
        self._add_to_memory("CRITICAL", title, message, kwargs)
    
    def log_api_request(self, method: str, url: str, status_code: int = None, 
                       response_time: float = None, **kwargs):
        """
        记录API请求日志
        
        Args:
            method: HTTP方法
            url: 请求URL
            status_code: 响应状态码
            response_time: 响应时间（秒）
        """
        message = f"{method} {url}"
        if status_code:
            message += f" -> {status_code}"
        if response_time:
            message += f" ({response_time:.2f}s)"
        
        level = "INFO"
        if status_code and status_code >= 400:
            level = "ERROR"
        elif status_code and status_code >= 300:
            level = "WARNING"
        
        extra_data = {
            'method': method,
            'url': url,
            'status_code': status_code,
            'response_time': response_time,
            **kwargs
        }
        
        if level == "ERROR":
            self.error("API请求", message, **extra_data)
        elif level == "WARNING":
            self.warning("API请求", message, **extra_data)
        else:
            self.info("API请求", message, **extra_data)
    
    def log_user_action(self, action: str, details: str = "", **kwargs):
        """
        记录用户操作日志
        
        Args:
            action: 操作类型
            details: 操作详情
        """
        message = f"用户操作: {action}"
        if details:
            message += f" - {details}"
        
        self.info("用户操作", message, action=action, details=details, **kwargs)
    
    def log_system_event(self, event: str, details: str = "", **kwargs):
        """
        记录系统事件日志
        
        Args:
            event: 事件类型
            details: 事件详情
        """
        message = f"系统事件: {event}"
        if details:
            message += f" - {details}"
        
        self.info("系统事件", message, event=event, details=details, **kwargs)
    
    def get_memory_logs(self, level_filter: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取内存中的日志
        
        Args:
            level_filter: 日志级别过滤
            limit: 返回数量限制
            
        Returns:
            日志列表
        """
        logs = self.memory_logs
        
        # 级别过滤
        if level_filter and level_filter != "ALL":
            logs = [log for log in logs if log['level'] == level_filter]
        
        # 数量限制
        if limit:
            logs = logs[-limit:]
        
        return logs
    
    def clear_memory_logs(self):
        """清空内存日志"""
        self.memory_logs.clear()
        self.info("日志管理", "内存日志已清空")
    
    def export_logs(self, file_path: str, format_type: str = "json") -> bool:
        """
        导出日志到文件
        
        Args:
            file_path: 导出文件路径
            format_type: 导出格式 (json, txt)
            
        Returns:
            导出是否成功
        """
        try:
            if format_type.lower() == "json":
                # JSON格式导出
                export_data = {
                    'export_time': datetime.now().isoformat(),
                    'logger_name': self.name,
                    'total_logs': len(self.memory_logs),
                    'logs': [
                        {
                            'timestamp': log['timestamp'].isoformat(),
                            'level': log['level'],
                            'title': log['title'],
                            'message': log['message'],
                            'extra_data': log['extra_data']
                        }
                        for log in self.memory_logs
                    ]
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            else:
                # 文本格式导出
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"团队管理工具日志导出\n")
                    f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"日志器: {self.name}\n")
                    f.write("=" * 80 + "\n\n")
                    
                    for log in self.memory_logs:
                        f.write(f"[{log['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}] ")
                        f.write(f"{log['level']}: {log['title']}\n")
                        f.write(f"  {log['message']}\n")
                        if log['extra_data']:
                            f.write(f"  额外数据: {log['extra_data']}\n")
                        f.write("\n")
            
            self.success("日志导出", f"日志已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.error("日志导出失败", f"导出到 {file_path} 失败", exception=e)
            return False
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            统计信息字典
        """
        if not self.memory_logs:
            return {'total': 0, 'by_level': {}}
        
        # 按级别统计
        level_counts = {}
        for log in self.memory_logs:
            level = log['level']
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # 时间范围
        timestamps = [log['timestamp'] for log in self.memory_logs]
        earliest = min(timestamps)
        latest = max(timestamps)
        
        return {
            'total': len(self.memory_logs),
            'by_level': level_counts,
            'time_range': {
                'earliest': earliest.isoformat(),
                'latest': latest.isoformat(),
                'span_hours': (latest - earliest).total_seconds() / 3600
            },
            'log_file': os.path.join(self.log_dir, f"{self.name.lower()}.log")
        }
    
    def set_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别名称
        """
        if level.upper() in LOG_LEVELS:
            self.logger.setLevel(LOG_LEVELS[level.upper()])
            self.info("日志配置", f"日志级别已设置为: {level.upper()}")
        else:
            self.warning("日志配置", f"无效的日志级别: {level}")


# 全局日志器实例
app_logger = Logger("TeamManager")
