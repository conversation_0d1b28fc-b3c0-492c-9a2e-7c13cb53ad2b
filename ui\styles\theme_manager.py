# -*- coding: utf-8 -*-
"""
主题管理器
负责管理应用程序的主题和样式
"""

import os
from typing import Dict, Optional
from PyQt6.QtWidgets import QApplication
from config.constants import COLOR_THEMES


class ThemeManager:
    """主题管理器"""
    
    def __init__(self, theme_dir: str = "themes"):
        """
        初始化主题管理器
        
        Args:
            theme_dir: 主题文件目录
        """
        self.theme_dir = theme_dir
        self.current_theme = "light"
        self.themes = COLOR_THEMES.copy()
        self._ensure_theme_dir()
    
    def _ensure_theme_dir(self):
        """确保主题目录存在"""
        if not os.path.exists(self.theme_dir):
            os.makedirs(self.theme_dir, exist_ok=True)
            # 创建默认主题文件
            self._create_default_themes()
    
    def _create_default_themes(self):
        """创建默认主题文件"""
        # 创建浅色主题
        light_theme = self._generate_theme_css("light")
        with open(os.path.join(self.theme_dir, "light.qss"), 'w', encoding='utf-8') as f:
            f.write(light_theme)
        
        # 创建深色主题
        dark_theme = self._generate_theme_css("dark")
        with open(os.path.join(self.theme_dir, "dark.qss"), 'w', encoding='utf-8') as f:
            f.write(dark_theme)
    
    def _generate_theme_css(self, theme_name: str) -> str:
        """
        生成主题CSS
        
        Args:
            theme_name: 主题名称
            
        Returns:
            CSS样式字符串
        """
        colors = self.themes.get(theme_name, self.themes["light"])
        
        return f"""
/* {theme_name.title()} Theme */

/* Main Window */
QMainWindow {{
    background: {colors['BACKGROUND']};
    color: {colors['TEXT']};
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}}

/* Widget Styles */
QWidget {{
    font-size: 13px;
    color: {colors['TEXT']};
}}

/* TabWidget Styles */
QTabWidget::pane {{
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    background: {colors['BACKGROUND']};
    border-radius: 8px;
    margin-top: -1px;
}}

QTabBar::tab {{
    background: {colors['NEUTRAL_LIGHT']};
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 10px 20px;
    margin-right: 2px;
    font-weight: 600;
    color: {colors['NEUTRAL_DARK']};
}}

QTabBar::tab:selected {{
    background: {colors['BACKGROUND']};
    color: {colors['PRIMARY']};
    border-bottom: 2px solid {colors['PRIMARY']};
}}

QTabBar::tab:hover:!selected {{
    background: {colors['NEUTRAL_MEDIUM']}80;
}}

/* Button Styles */
QPushButton {{
    background: {colors['NEUTRAL_LIGHT']};
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    color: {colors['TEXT']};
    min-height: 20px;
}}

QPushButton:hover {{
    background: {colors['NEUTRAL_MEDIUM']};
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
}}

QPushButton:pressed {{
    background: {colors['NEUTRAL_MEDIUM']};
}}

QPushButton:disabled {{
    background: {colors['NEUTRAL_LIGHT']};
    color: {colors['NEUTRAL_MEDIUM']};
    border: 1px solid {colors['NEUTRAL_MEDIUM']}80;
}}

/* Primary Button */
QPushButton[class="primary"] {{
    background: {colors['PRIMARY']};
    border: 1px solid {colors['PRIMARY_DARK']};
    color: white;
}}

QPushButton[class="primary"]:hover {{
    background: {colors['PRIMARY_DARK']};
}}

/* Success Button */
QPushButton[class="success"] {{
    background: {colors['SUCCESS']};
    border: 1px solid {colors['SUCCESS']};
    color: white;
}}

/* Danger Button */
QPushButton[class="danger"] {{
    background: {colors['DANGER']};
    border: 1px solid {colors['DANGER']};
    color: white;
}}

/* Warning Button */
QPushButton[class="warning"] {{
    background: {colors['WARNING']};
    border: 1px solid {colors['WARNING']};
    color: white;
}}

/* Info Button */
QPushButton[class="info"] {{
    background: {colors['INFO']};
    border: 1px solid {colors['INFO']};
    color: white;
}}

/* GroupBox Styles */
QGroupBox {{
    font-weight: 700;
    font-size: 14px;
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 8px;
    margin-top: 16px;
    padding-top: 24px;
    background: {colors['BACKGROUND']};
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    left: 10px;
    padding: 5px 10px;
    color: {colors['PRIMARY']};
}}

/* Text Input Styles */
QLineEdit, QTextEdit {{
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 6px;
    padding: 8px 12px;
    background: {colors['BACKGROUND']};
    selection-background-color: {colors['PRIMARY']}40;
    font-size: 13px;
    color: {colors['TEXT']};
}}

QLineEdit:focus, QTextEdit:focus {{
    border-color: {colors['PRIMARY']};
    background: {colors['BACKGROUND']};
}}

/* Table Styles */
QTableWidget {{
    gridline-color: {colors['NEUTRAL_MEDIUM']};
    background: {colors['BACKGROUND']};
    alternate-background-color: {colors['NEUTRAL_LIGHT']};
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 8px;
    selection-background-color: {colors['PRIMARY']}20;
    selection-color: {colors['PRIMARY']};
}}

QTableWidget::item {{
    padding: 6px;
    border-bottom: 1px solid {colors['NEUTRAL_MEDIUM']}40;
}}

QTableWidget::item:selected {{
    background: {colors['PRIMARY']}20;
    color: {colors['PRIMARY_DARK']};
    font-weight: 600;
}}

QHeaderView::section {{
    background: {colors['PRIMARY']};
    padding: 8px;
    border: none;
    font-weight: 600;
    color: white;
}}

/* Progress Bar */
QProgressBar {{
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 4px;
    text-align: center;
    background: {colors['NEUTRAL_LIGHT']};
    height: 14px;
    color: {colors['TEXT']};
}}

QProgressBar::chunk {{
    background: {colors['PRIMARY']};
    border-radius: 3px;
}}

/* Status Bar */
QStatusBar {{
    background: {colors['NEUTRAL_LIGHT']};
    border-top: 1px solid {colors['NEUTRAL_MEDIUM']};
    color: {colors['TEXT']};
}}

/* Scrollbar Styles */
QScrollBar:vertical {{
    background: {colors['NEUTRAL_LIGHT']};
    width: 10px;
    border-radius: 5px;
}}

QScrollBar::handle:vertical {{
    background: {colors['NEUTRAL_MEDIUM']};
    border-radius: 5px;
    min-height: 20px;
}}

QScrollBar::handle:vertical:hover {{
    background: {colors['PRIMARY']}60;
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0px;
}}

/* ComboBox Styles */
QComboBox {{
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 6px;
    padding: 8px 12px;
    background: {colors['BACKGROUND']};
    min-width: 6em;
}}

QComboBox:focus {{
    border-color: {colors['PRIMARY']};
}}

/* Checkbox Styles */
QCheckBox {{
    spacing: 8px;
}}

QCheckBox::indicator {{
    width: 18px;
    height: 18px;
    border: 1px solid {colors['NEUTRAL_MEDIUM']};
    border-radius: 4px;
    background: {colors['BACKGROUND']};
}}

QCheckBox::indicator:checked {{
    background: {colors['PRIMARY']};
    border-color: {colors['PRIMARY']};
}}

QCheckBox::indicator:hover {{
    border-color: {colors['PRIMARY']};
}}
        """
    
    def load_theme(self, theme_name: str) -> str:
        """
        加载主题样式
        
        Args:
            theme_name: 主题名称
            
        Returns:
            CSS样式字符串
        """
        theme_file = os.path.join(self.theme_dir, f"{theme_name}.qss")
        
        if os.path.exists(theme_file):
            try:
                with open(theme_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"❌ 加载主题文件失败: {e}")
        
        # 如果文件不存在，生成默认主题
        return self._generate_theme_css(theme_name)
    
    def apply_theme(self, theme_name: str, app: Optional[QApplication] = None):
        """
        应用主题
        
        Args:
            theme_name: 主题名称
            app: QApplication实例，如果为None则自动获取
        """
        if app is None:
            app = QApplication.instance()
        
        if app is None:
            print("❌ 无法获取QApplication实例")
            return
        
        css = self.load_theme(theme_name)
        app.setStyleSheet(css)
        self.current_theme = theme_name
        print(f"✅ 已应用主题: {theme_name}")
    
    def get_available_themes(self) -> list:
        """
        获取可用主题列表
        
        Returns:
            主题名称列表
        """
        themes = list(self.themes.keys())
        
        # 扫描主题目录中的.qss文件
        if os.path.exists(self.theme_dir):
            for file in os.listdir(self.theme_dir):
                if file.endswith('.qss'):
                    theme_name = file[:-4]  # 移除.qss扩展名
                    if theme_name not in themes:
                        themes.append(theme_name)
        
        return themes
    
    def get_theme_colors(self, theme_name: str) -> Dict[str, str]:
        """
        获取主题颜色配置
        
        Args:
            theme_name: 主题名称
            
        Returns:
            颜色配置字典
        """
        return self.themes.get(theme_name, self.themes["light"])
    
    def create_custom_theme(self, theme_name: str, colors: Dict[str, str]):
        """
        创建自定义主题
        
        Args:
            theme_name: 主题名称
            colors: 颜色配置
        """
        self.themes[theme_name] = colors
        css = self._generate_theme_css(theme_name)
        
        theme_file = os.path.join(self.theme_dir, f"{theme_name}.qss")
        with open(theme_file, 'w', encoding='utf-8') as f:
            f.write(css)
        
        print(f"✅ 自定义主题已创建: {theme_name}")
    
    def get_current_theme(self) -> str:
        """
        获取当前主题名称
        
        Returns:
            当前主题名称
        """
        return self.current_theme
