/* Dark Theme - 团队管理工具 */

/* Main Window */
QMainWindow {
    background: #1f2937;
    color: #f9fafb;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* Widget Styles */
QWidget {
    font-size: 13px;
    color: #f9fafb;
}

/* TabWidget Styles */
QTabWidget::pane {
    border: 1px solid #4b5563;
    background: #1f2937;
    border-radius: 8px;
    margin-top: -1px;
}

QTabBar::tab {
    background: #374151;
    border: 1px solid #4b5563;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 10px 20px;
    margin-right: 2px;
    font-weight: 600;
    color: #111827;
}

QTabBar::tab:selected {
    background: #1f2937;
    color: #6366f1;
    border-bottom: 2px solid #6366f1;
}

QTabBar::tab:hover:!selected {
    background: #4b556380;
}

/* Button Styles */
QPushButton {
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    color: #f9fafb;
    min-height: 20px;
}

QPushButton:hover {
    background: #4b5563;
    border: 1px solid #4b5563;
}

QPushButton:pressed {
    background: #4b5563;
}

QPushButton:disabled {
    background: #374151;
    color: #4b5563;
    border: 1px solid #4b556380;
}

/* Primary Button */
QPushButton[class="primary"] {
    background: #6366f1;
    border: 1px solid #4338ca;
    color: white;
}

QPushButton[class="primary"]:hover {
    background: #4338ca;
}

/* Success Button */
QPushButton[class="success"] {
    background: #10b981;
    border: 1px solid #059669;
    color: white;
}

QPushButton[class="success"]:hover {
    background: #059669;
}

/* Danger Button */
QPushButton[class="danger"] {
    background: #f87171;
    border: 1px solid #ef4444;
    color: white;
}

QPushButton[class="danger"]:hover {
    background: #ef4444;
}

/* Warning Button */
QPushButton[class="warning"] {
    background: #fbbf24;
    border: 1px solid #f59e0b;
    color: white;
}

QPushButton[class="warning"]:hover {
    background: #f59e0b;
}

/* Info Button */
QPushButton[class="info"] {
    background: #22d3ee;
    border: 1px solid #06b6d4;
    color: white;
}

QPushButton[class="info"]:hover {
    background: #06b6d4;
}

/* GroupBox Styles */
QGroupBox {
    font-weight: 700;
    font-size: 14px;
    border: 1px solid #4b5563;
    border-radius: 8px;
    margin-top: 16px;
    padding-top: 24px;
    background: #1f2937;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 5px 10px;
    color: #6366f1;
}

/* Text Input Styles */
QLineEdit, QTextEdit {
    border: 1px solid #4b5563;
    border-radius: 6px;
    padding: 8px 12px;
    background: #1f2937;
    selection-background-color: #6366f140;
    font-size: 13px;
    color: #f9fafb;
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #6366f1;
    background: #1f2937;
}

/* Table Styles */
QTableWidget {
    gridline-color: #4b5563;
    background: #1f2937;
    alternate-background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 8px;
    selection-background-color: #6366f120;
    selection-color: #6366f1;
}

QTableWidget::item {
    padding: 6px;
    border-bottom: 1px solid #4b556340;
}

QTableWidget::item:selected {
    background: #6366f120;
    color: #8b5cf6;
    font-weight: 600;
}

QHeaderView::section {
    background: #6366f1;
    padding: 8px;
    border: none;
    font-weight: 600;
    color: white;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #4b5563;
    border-radius: 4px;
    text-align: center;
    background: #374151;
    height: 14px;
    color: #f9fafb;
}

QProgressBar::chunk {
    background: #6366f1;
    border-radius: 3px;
}

/* Status Bar */
QStatusBar {
    background: #374151;
    border-top: 1px solid #4b5563;
    color: #f9fafb;
}

/* Menu Styles */
QMenuBar {
    background: #1f2937;
    border-bottom: 1px solid #4b5563;
    color: #f9fafb;
}

QMenuBar::item {
    padding: 6px 10px;
    background: transparent;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background: #6366f120;
    color: #6366f1;
}

QMenu {
    background: #1f2937;
    border: 1px solid #4b5563;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
}

QMenu::item:selected {
    background: #6366f120;
    color: #6366f1;
}

/* Scrollbar Styles */
QScrollBar:vertical {
    background: #374151;
    width: 10px;
    border-radius: 5px;
}

QScrollBar::handle:vertical {
    background: #4b5563;
    border-radius: 5px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #6366f160;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* ComboBox Styles */
QComboBox {
    border: 1px solid #4b5563;
    border-radius: 6px;
    padding: 8px 12px;
    background: #1f2937;
    min-width: 6em;
}

QComboBox:focus {
    border-color: #6366f1;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

/* Checkbox Styles */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #4b5563;
    border-radius: 4px;
    background: #1f2937;
}

QCheckBox::indicator:checked {
    background: #6366f1;
    border-color: #6366f1;
}

QCheckBox::indicator:hover {
    border-color: #6366f1;
}
