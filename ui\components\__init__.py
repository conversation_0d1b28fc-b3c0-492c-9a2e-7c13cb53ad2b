# -*- coding: utf-8 -*-
"""
UI组件模块
提供可复用的UI组件和通信机制
"""

from .event_bus import EventBus, EventType
from .progress_widget import ProgressWidget
from .stats_widget import StatsWidget, StatCard, ProgressCard
from .virtual_table import VirtualTable, VirtualTableModel

# 全局事件总线实例
_global_event_bus = None

def get_event_bus() -> EventBus:
    """
    获取全局事件总线实例

    Returns:
        EventBus: 全局事件总线
    """
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus

__all__ = [
    'EventBus',
    'EventType',
    'ProgressWidget',
    'StatsWidget',
    'StatCard',
    'ProgressCard',
    'VirtualTable',
    'VirtualTableModel',
    'get_event_bus'
]
