# -*- coding: utf-8 -*-
"""
API数据模型
定义API响应的数据结构
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class MemberData:
    """团队成员数据模型"""
    id: str
    email: str
    name: Optional[str] = None
    role: Optional[str] = None
    status: Optional[str] = None
    joined_at: Optional[datetime] = None
    last_active: Optional[datetime] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemberData':
        """从字典创建成员数据"""
        return cls(
            id=data.get('id', ''),
            email=data.get('email', ''),
            name=data.get('name'),
            role=data.get('role'),
            status=data.get('status'),
            joined_at=cls._parse_datetime(data.get('joined_at')),
            last_active=cls._parse_datetime(data.get('last_active'))
        )
    
    @staticmethod
    def _parse_datetime(date_str: Optional[str]) -> Optional[datetime]:
        """解析日期时间字符串"""
        if not date_str:
            return None
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            return None


@dataclass
class InvitationData:
    """邀请数据模型"""
    id: str
    email: str
    status: str
    invited_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    invited_by: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InvitationData':
        """从字典创建邀请数据"""
        return cls(
            id=data.get('id', ''),
            email=data.get('email', ''),
            status=data.get('status', 'pending'),
            invited_at=MemberData._parse_datetime(data.get('invited_at')),
            expires_at=MemberData._parse_datetime(data.get('expires_at')),
            invited_by=data.get('invited_by')
        )
    
    @property
    def is_pending(self) -> bool:
        """是否为待处理状态"""
        return self.status.lower() == 'pending'
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at


@dataclass
class TeamData:
    """团队数据模型"""
    id: str
    name: str
    members: List[MemberData]
    invitations: List[InvitationData]
    plan: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TeamData':
        """从字典创建团队数据"""
        members = []
        if 'members' in data:
            members = [MemberData.from_dict(m) for m in data['members']]
        
        invitations = []
        if 'invitations' in data:
            invitations = [InvitationData.from_dict(i) for i in data['invitations']]
        elif 'pendingMembers' in data:
            # 兼容旧格式
            invitations = [InvitationData.from_dict({'id': str(i), 'email': email, 'status': 'pending'}) 
                          for i, email in enumerate(data['pendingMembers'])]
        
        return cls(
            id=data.get('id', ''),
            name=data.get('name', ''),
            members=members,
            invitations=invitations,
            plan=data.get('plan'),
            created_at=MemberData._parse_datetime(data.get('created_at')),
            updated_at=MemberData._parse_datetime(data.get('updated_at'))
        )
    
    @property
    def total_members(self) -> int:
        """总成员数"""
        return len(self.members)
    
    @property
    def pending_invitations(self) -> List[InvitationData]:
        """待处理邀请列表"""
        return [inv for inv in self.invitations if inv.is_pending]
    
    @property
    def pending_count(self) -> int:
        """待处理邀请数量"""
        return len(self.pending_invitations)
    
    @property
    def active_members(self) -> List[MemberData]:
        """活跃成员列表"""
        return [member for member in self.members if member.status == 'active']
    
    @property
    def today_invitations(self) -> List[InvitationData]:
        """今日邀请列表"""
        today = datetime.now().date()
        return [inv for inv in self.invitations 
                if inv.invited_at and inv.invited_at.date() == today]
    
    @property
    def today_invitations_count(self) -> int:
        """今日邀请数量"""
        return len(self.today_invitations)
    
    def get_member_by_email(self, email: str) -> Optional[MemberData]:
        """根据邮箱获取成员"""
        for member in self.members:
            if member.email.lower() == email.lower():
                return member
        return None
    
    def get_invitation_by_email(self, email: str) -> Optional[InvitationData]:
        """根据邮箱获取邀请"""
        for invitation in self.invitations:
            if invitation.email.lower() == email.lower():
                return invitation
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'plan': self.plan,
            'total_members': self.total_members,
            'pending_count': self.pending_count,
            'today_invitations_count': self.today_invitations_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'members': [
                {
                    'id': m.id,
                    'email': m.email,
                    'name': m.name,
                    'role': m.role,
                    'status': m.status,
                    'joined_at': m.joined_at.isoformat() if m.joined_at else None,
                    'last_active': m.last_active.isoformat() if m.last_active else None
                }
                for m in self.members
            ],
            'invitations': [
                {
                    'id': i.id,
                    'email': i.email,
                    'status': i.status,
                    'invited_at': i.invited_at.isoformat() if i.invited_at else None,
                    'expires_at': i.expires_at.isoformat() if i.expires_at else None,
                    'invited_by': i.invited_by
                }
                for i in self.invitations
            ]
        }


@dataclass
class APIResponse:
    """API响应模型"""
    success: bool
    data: Any = None
    message: str = ""
    error_code: Optional[str] = None
    status_code: Optional[int] = None
    
    @classmethod
    def success_response(cls, data: Any = None, message: str = "操作成功") -> 'APIResponse':
        """创建成功响应"""
        return cls(success=True, data=data, message=message)
    
    @classmethod
    def error_response(cls, message: str, error_code: Optional[str] = None, 
                      status_code: Optional[int] = None) -> 'APIResponse':
        """创建错误响应"""
        return cls(success=False, message=message, error_code=error_code, status_code=status_code)
    
    def __bool__(self) -> bool:
        """支持布尔判断"""
        return self.success
