# -*- coding: utf-8 -*-
"""
应用程序常量定义
"""

# API相关常量
API_ENDPOINTS = {
    'BASE_URL': 'https://app.augmentcode.com/api',
    'TEAM': '/team',
    'INVITE': '/team/invite',
    'DELETE_MEMBER': '/team/invite/{member_id}',
    'PUT_USER_ON_PLAN': '/put-user-on-plan'
}

# 计划ID常量
PLAN_IDS = {
    'COMMUNITY': 'orb_community_plan',
    'MAX': 'orb_max_plan'
}

# UI常量
UI_CONSTANTS = {
    'WINDOW_MIN_WIDTH': 1000,
    'WINDOW_MIN_HEIGHT': 700,
    'WINDOW_DEFAULT_WIDTH': 1200,
    'WINDOW_DEFAULT_HEIGHT': 800,
    'LOG_MAX_ENTRIES': 1000,
    'LOG_DISPLAY_ENTRIES': 100
}

# 默认设置
DEFAULT_SETTINGS = {
    "api": {
        "base_url": API_ENDPOINTS['BASE_URL'],
        "timeout": 30,
        "headers": {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://app.augmentcode.com/account/subscription",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
    },
    "ui": {
        "theme": "light",
        "font_size": 10,
        "auto_refresh": True,
        "refresh_interval": 30,
        "opacity": 100,
        "start_minimized": False,
        "close_to_tray": False
    },
    "features": {
        "batch_operations": True,
        "email_validation": True,
        "auto_save": True,
        "debug_mode": False
    },
    "security": {
        "encrypt_cookies": True,
        "session_timeout": 3600
    }
}

# 颜色主题
COLOR_THEMES = {
    'light': {
        'PRIMARY': '#4361ee',
        'PRIMARY_LIGHT': '#7b9cff',
        'PRIMARY_DARK': '#2c3ebb',
        'SECONDARY': '#3f37c9',
        'SUCCESS': '#22c55e',
        'DANGER': '#ef4444',
        'WARNING': '#f97316',
        'INFO': '#06b6d4',
        'BACKGROUND': '#ffffff',
        'TEXT': '#0f172a',
        'NEUTRAL_LIGHT': '#f8fafc',
        'NEUTRAL_MEDIUM': '#e2e8f0',
        'NEUTRAL_DARK': '#1e293b'
    },
    'dark': {
        'PRIMARY': '#6366f1',
        'PRIMARY_LIGHT': '#8b5cf6',
        'PRIMARY_DARK': '#4338ca',
        'SECONDARY': '#7c3aed',
        'SUCCESS': '#10b981',
        'DANGER': '#f87171',
        'WARNING': '#fbbf24',
        'INFO': '#22d3ee',
        'BACKGROUND': '#1f2937',
        'TEXT': '#f9fafb',
        'NEUTRAL_LIGHT': '#374151',
        'NEUTRAL_MEDIUM': '#4b5563',
        'NEUTRAL_DARK': '#111827'
    }
}

# 日志级别
LOG_LEVELS = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50
}

# 文件路径
FILE_PATHS = {
    'CONFIG': 'team_manager_config.json',
    'LOGS': 'logs/',
    'THEMES': 'themes/',
    'CACHE': 'cache/'
}
