#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队管理工具 - 优化版启动脚本
使用重构后的模块提供更好的性能和用户体验

作者: Claude 4.0 sonnet
版本: 2.0.0
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QLabel
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_splash_screen(app):
    """显示启动画面"""
    from PyQt6.QtWidgets import QWidget, QVBoxLayout

    # 创建一个自定义的启动画面窗口
    class CustomSplashScreen(QWidget):
        def __init__(self):
            super().__init__()
            self.setFixedSize(400, 300)
            self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

            # 设置样式
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #4361ee, stop:1 #7b9cff);
                    border-radius: 15px;
                }
            """)

            # 创建布局
            layout = QVBoxLayout(self)
            layout.setContentsMargins(40, 40, 40, 40)
            layout.setSpacing(20)

            # 标题
            self.title = QLabel("🚀 团队管理工具")
            self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_font = QFont()
            title_font.setPointSize(24)
            title_font.setBold(True)
            self.title.setFont(title_font)
            self.title.setStyleSheet("color: white;")

            # 版本信息
            self.version = QLabel("版本 2.0 - 重构优化版")
            self.version.setAlignment(Qt.AlignmentFlag.AlignCenter)
            version_font = QFont()
            version_font.setPointSize(12)
            self.version.setFont(version_font)
            self.version.setStyleSheet("color: rgba(255, 255, 255, 0.8);")

            # 状态信息
            self.status = QLabel("正在初始化...")
            self.status.setAlignment(Qt.AlignmentFlag.AlignCenter)
            status_font = QFont()
            status_font.setPointSize(10)
            self.status.setFont(status_font)
            self.status.setStyleSheet("color: rgba(255, 255, 255, 0.7);")

            layout.addStretch()
            layout.addWidget(self.title)
            layout.addWidget(self.version)
            layout.addStretch()
            layout.addWidget(self.status)

            # 居中显示
            self.center_on_screen()

        def center_on_screen(self):
            """将窗口居中显示"""
            screen = app.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)

    # 创建并显示启动画面
    splash = CustomSplashScreen()
    splash.show()
    app.processEvents()

    return splash, splash.status

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import aiohttp
    except ImportError:
        missing_deps.append("aiohttp")
    
    try:
        from cachetools import TTLCache
    except ImportError:
        missing_deps.append("cachetools")
    
    try:
        from cryptography.fernet import Fernet
    except ImportError:
        missing_deps.append("cryptography")
    
    return missing_deps

def install_missing_dependencies(missing_deps):
    """安装缺失的依赖项"""
    if not missing_deps:
        return True
    
    print(f"⚠️ 检测到缺失的依赖项: {', '.join(missing_deps)}")
    print("🔄 正在尝试自动安装...")
    
    import subprocess
    
    for dep in missing_deps:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ 成功安装: {dep}")
        except subprocess.CalledProcessError:
            print(f"❌ 安装失败: {dep}")
            return False
    
    return True

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("团队管理工具")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("Team Manager")
    
    # 显示启动画面
    splash, status_label = show_splash_screen(app)
    
    try:
        # 检查依赖项
        status_label.setText("检查依赖项...")
        app.processEvents()
        
        missing_deps = check_dependencies()
        if missing_deps:
            status_label.setText(f"安装依赖项: {', '.join(missing_deps)}")
            app.processEvents()
            
            if not install_missing_dependencies(missing_deps):
                status_label.setText("❌ 依赖项安装失败")
                app.processEvents()
                QTimer.singleShot(3000, app.quit)
                return app.exec()
        
        # 尝试导入重构模块
        status_label.setText("加载重构模块...")
        app.processEvents()
        
        try:
            from config import Config
            from api import APIClient
            from ui.styles import ThemeManager, StyleManager
            from ui.dialogs import CustomMessageBox
            from ui.components import EventBus, ProgressWidget, StatsWidget, VirtualTable
            from utils import Logger, SecurityManager
            from utils.task_queue import get_task_queue
            
            use_optimized = True
            status_label.setText("✅ 重构模块加载成功")
            print("✅ 使用重构优化版本")
            
        except ImportError as e:
            print(f"⚠️ 重构模块导入失败: {e}")
            use_optimized = False
            status_label.setText("⚠️ 使用原始版本")
        
        app.processEvents()
        
        # 导入主窗口
        status_label.setText("初始化主窗口...")
        app.processEvents()
        
        if use_optimized:
            # 使用优化版本
            from main import Application
            
            # 创建应用程序实例
            team_app = Application()
            
            # 初始化
            if not team_app.initialize():
                status_label.setText("❌ 应用初始化失败")
                app.processEvents()
                QTimer.singleShot(3000, app.quit)
                return app.exec()
            
            status_label.setText("🎉 启动完成！")
            app.processEvents()
            
            # 延迟关闭启动画面
            QTimer.singleShot(1500, splash.close)
            
            # 运行应用
            return team_app.run()
            
        else:
            # 使用原始版本
            from team_manager import TeamManagerMainWindow
            
            # 创建主窗口
            main_window = TeamManagerMainWindow()
            main_window.show()
            
            status_label.setText("🎉 启动完成！")
            app.processEvents()
            
            # 延迟关闭启动画面
            QTimer.singleShot(1500, splash.close)
            
            print("⚠️ 使用原始版本启动")
            return app.exec()
    
    except Exception as e:
        status_label.setText(f"❌ 启动失败: {str(e)}")
        app.processEvents()
        
        print(f"❌ 应用启动失败: {str(e)}")
        
        # 显示错误信息
        try:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(None, "启动错误", 
                               f"应用启动失败：\n\n{str(e)}\n\n"
                               f"请检查依赖项是否正确安装。")
        except:
            pass
        
        QTimer.singleShot(5000, app.quit)
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
