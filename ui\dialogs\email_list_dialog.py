# -*- coding: utf-8 -*-
"""
邮箱列表对话框组件
提供邮箱列表的显示和操作功能
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QGroupBox, QFileDialog, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from typing import List

from ui.styles import StyleManager


class PendingEmailsDialog(QDialog):
    """未接受邮箱列表对话框"""
    
    # 信号定义
    emails_copied = pyqtSignal(list)  # 邮箱复制信号
    emails_exported = pyqtSignal(str)  # 邮箱导出信号
    
    def __init__(self, emails: List[str], parent=None):
        """
        初始化邮箱列表对话框
        
        Args:
            emails: 邮箱列表
            parent: 父组件
        """
        super().__init__(parent)
        self.emails = emails
        self.setWindowTitle("📧 未接受邀请的邮箱列表")
        self.setModal(True)
        self.resize(600, 500)
        self.setMinimumSize(500, 400)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        title_icon = QLabel("📧")
        title_icon.setStyleSheet("font-size: 24px;")
        
        title_label = QLabel(f"未接受邀请的邮箱列表 ({len(self.emails)} 个)")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleManager.get_color('PRIMARY')};")
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # 邮箱列表显示区域
        list_group = QGroupBox("邮箱地址")
        list_layout = QVBoxLayout(list_group)
        
        # 说明文本
        info_label = QLabel("以下邮箱地址尚未接受邀请，每行一个邮箱地址：")
        info_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')};")
        
        # 邮箱文本编辑器
        self.email_text = QTextEdit()
        self.email_text.setPlainText('\n'.join(self.emails))
        self.email_text.setReadOnly(True)
        self.email_text.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 6px;
                padding: 10px;
                background: {StyleManager.get_color('BACKGROUND')};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                line-height: 1.4;
            }}
        """)
        
        # 统计信息
        stats_label = QLabel(f"总计: {len(self.emails)} 个邮箱地址")
        stats_label.setStyleSheet(f"""
            QLabel {{
                color: {StyleManager.get_color('NEUTRAL_DARK')};
                font-size: 12px;
                padding: 5px;
                background: {StyleManager.get_color('NEUTRAL_LIGHT')};
                border-radius: 4px;
            }}
        """)
        
        list_layout.addWidget(info_label)
        list_layout.addWidget(self.email_text)
        list_layout.addWidget(stats_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 全选按钮
        select_all_btn = StyleManager.create_button("📋 全选", "info")
        select_all_btn.clicked.connect(self.select_all_emails)
        select_all_btn.setToolTip("选择所有邮箱地址")
        
        # 复制按钮
        copy_btn = StyleManager.create_button("📄 复制", "primary")
        copy_btn.clicked.connect(self.copy_emails)
        copy_btn.setToolTip("复制邮箱列表到剪贴板")
        
        # 导出按钮
        export_btn = StyleManager.create_button("💾 导出", "success")
        export_btn.clicked.connect(self.export_to_file)
        export_btn.setToolTip("导出邮箱列表到文本文件")
        
        # 关闭按钮
        close_btn = StyleManager.create_button("❌ 关闭", "")
        close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        # 组装布局
        layout.addLayout(title_layout)
        layout.addWidget(list_group)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 应用样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {StyleManager.get_color('BACKGROUND')};
            }}
        """)
        
        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))
    
    def select_all_emails(self):
        """全选所有邮箱地址"""
        self.email_text.selectAll()
    
    def copy_emails(self):
        """复制邮箱列表到剪贴板"""
        clipboard = QApplication.clipboard()
        email_text = '\n'.join(self.emails)
        clipboard.setText(email_text)
        
        # 发射信号
        self.emails_copied.emit(self.emails)
        
        # 显示成功提示
        from ui.dialogs import CustomMessageBox
        CustomMessageBox.show_success(
            self, "复制成功",
            f"已复制 {len(self.emails)} 个邮箱地址到剪贴板\n\n"
            "每行一个邮箱，方便粘贴使用。"
        )
    
    def export_to_file(self):
        """导出邮箱列表到文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出邮箱列表",
            f"pending_emails_{len(self.emails)}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(self.emails))
                
                # 发射信号
                self.emails_exported.emit(file_path)
                
                # 显示成功提示
                from ui.dialogs import CustomMessageBox
                CustomMessageBox.show_success(
                    self, "导出成功",
                    f"邮箱列表已成功导出到:\n{file_path}"
                )
            except Exception as e:
                # 显示错误提示
                from ui.dialogs import CustomMessageBox
                CustomMessageBox.show_error(
                    self, "导出失败", 
                    f"导出文件时发生错误:\n{str(e)}"
                )
    
    def get_emails(self) -> List[str]:
        """
        获取邮箱列表
        
        Returns:
            List[str]: 邮箱列表
        """
        return self.emails.copy()
    
    def update_emails(self, emails: List[str]):
        """
        更新邮箱列表
        
        Args:
            emails: 新的邮箱列表
        """
        self.emails = emails
        self.email_text.setPlainText('\n'.join(self.emails))
        
        # 更新标题
        title_text = f"未接受邀请的邮箱列表 ({len(self.emails)} 个)"
        self.setWindowTitle(f"📧 {title_text}")
    
    @staticmethod
    def show_emails_dialog(emails: List[str], parent=None):
        """
        显示邮箱列表对话框
        
        Args:
            emails: 邮箱列表
            parent: 父组件
            
        Returns:
            PendingEmailsDialog: 对话框实例
        """
        dialog = PendingEmailsDialog(emails, parent)
        dialog.show()
        return dialog


class EmailInputDialog(QDialog):
    """邮箱输入对话框"""
    
    def __init__(self, parent=None):
        """
        初始化邮箱输入对话框
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        self.setWindowTitle("📧 批量邀请成员")
        self.setModal(True)
        self.resize(500, 400)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📧 批量邀请成员")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleManager.get_color('PRIMARY')};")
        
        # 输入区域
        input_group = QGroupBox("邮箱地址")
        input_layout = QVBoxLayout(input_group)
        
        # 说明文本
        info_label = QLabel("请输入要邀请的邮箱地址，每行一个：")
        info_label.setStyleSheet(f"color: {StyleManager.get_color('NEUTRAL_DARK')};")
        
        # 邮箱输入框
        self.email_input = QTextEdit()
        self.email_input.setPlaceholderText(
            "<EMAIL>\n"
            "<EMAIL>\n"
            "<EMAIL>"
        )
        self.email_input.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid {StyleManager.get_color('NEUTRAL_MEDIUM')};
                border-radius: 6px;
                padding: 10px;
                background: {StyleManager.get_color('BACKGROUND')};
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                line-height: 1.4;
            }}
            
            QTextEdit:focus {{
                border-color: {StyleManager.get_color('PRIMARY')};
            }}
        """)
        
        input_layout.addWidget(info_label)
        input_layout.addWidget(self.email_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 验证按钮
        validate_btn = StyleManager.create_button("🔍 验证邮箱", "info")
        validate_btn.clicked.connect(self.validate_emails)
        
        # 取消按钮
        cancel_btn = StyleManager.create_button("❌ 取消", "")
        cancel_btn.clicked.connect(self.reject)
        
        # 确定按钮
        ok_btn = StyleManager.create_button("✅ 确定", "primary")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        
        button_layout.addWidget(validate_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(ok_btn)
        
        # 组装布局
        layout.addWidget(title_label)
        layout.addWidget(input_group)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 应用样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {StyleManager.get_color('BACKGROUND')};
            }}
        """)
        
        # 添加阴影效果
        StyleManager.apply_shadow_effect(self, blur_radius=20, offset=(0, 10))
    
    def validate_emails(self):
        """验证邮箱格式"""
        emails = self.get_emails()
        if not emails:
            from ui.dialogs import CustomMessageBox
            CustomMessageBox.show_warning(self, "验证邮箱", "请先输入邮箱地址")
            return
        
        # 这里可以添加邮箱验证逻辑
        from ui.dialogs import CustomMessageBox
        CustomMessageBox.show_info(
            self, "验证结果", 
            f"共输入 {len(emails)} 个邮箱地址\n\n"
            "邮箱格式验证功能待完善..."
        )
    
    def get_emails(self) -> List[str]:
        """
        获取输入的邮箱列表
        
        Returns:
            List[str]: 邮箱列表
        """
        text = self.email_input.toPlainText().strip()
        if not text:
            return []
        
        # 分割并清理邮箱地址
        emails = []
        for line in text.split('\n'):
            email = line.strip()
            if email and '@' in email:
                emails.append(email)
        
        return emails
    
    @staticmethod
    def get_emails_input(parent=None) -> List[str]:
        """
        显示邮箱输入对话框并获取输入
        
        Args:
            parent: 父组件
            
        Returns:
            List[str]: 输入的邮箱列表，如果取消则返回空列表
        """
        dialog = EmailInputDialog(parent)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            return dialog.get_emails()
        return []
