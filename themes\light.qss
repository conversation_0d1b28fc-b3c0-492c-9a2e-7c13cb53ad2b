/* Light Theme - 团队管理工具 */

/* Main Window */
QMainWindow {
    background: #f1f5f9;
    color: #0f172a;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* Widget Styles */
QWidget {
    font-size: 13px;
    color: #0f172a;
}

/* TabWidget Styles */
QTabWidget::pane {
    border: 1px solid #e2e8f0;
    background: #ffffff;
    border-radius: 8px;
    margin-top: -1px;
}

QTabBar::tab {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 10px 20px;
    margin-right: 2px;
    font-weight: 600;
    color: #1e293b;
}

QTabBar::tab:selected {
    background: #ffffff;
    color: #4361ee;
    border-bottom: 2px solid #4361ee;
}

QTabBar::tab:hover:!selected {
    background: #e2e8f080;
}

/* Button Styles */
QPushButton {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    color: #0f172a;
    min-height: 20px;
}

QPushButton:hover {
    background: #e2e8f0;
    border: 1px solid #e2e8f0;
}

QPushButton:pressed {
    background: #e2e8f0;
}

QPushButton:disabled {
    background: #f8fafc;
    color: #e2e8f0;
    border: 1px solid #e2e8f080;
}

/* Primary Button */
QPushButton[class="primary"] {
    background: #4361ee;
    border: 1px solid #2c3ebb;
    color: white;
}

QPushButton[class="primary"]:hover {
    background: #2c3ebb;
}

/* Success Button */
QPushButton[class="success"] {
    background: #22c55e;
    border: 1px solid #16a34a;
    color: white;
}

QPushButton[class="success"]:hover {
    background: #16a34a;
}

/* Danger Button */
QPushButton[class="danger"] {
    background: #ef4444;
    border: 1px solid #dc2626;
    color: white;
}

QPushButton[class="danger"]:hover {
    background: #dc2626;
}

/* Warning Button */
QPushButton[class="warning"] {
    background: #f97316;
    border: 1px solid #ea580c;
    color: white;
}

QPushButton[class="warning"]:hover {
    background: #ea580c;
}

/* Info Button */
QPushButton[class="info"] {
    background: #06b6d4;
    border: 1px solid #0891b2;
    color: white;
}

QPushButton[class="info"]:hover {
    background: #0891b2;
}

/* GroupBox Styles */
QGroupBox {
    font-weight: 700;
    font-size: 14px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-top: 16px;
    padding-top: 24px;
    background: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 5px 10px;
    color: #4361ee;
}

/* Text Input Styles */
QLineEdit, QTextEdit {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 12px;
    background: #ffffff;
    selection-background-color: #4361ee40;
    font-size: 13px;
    color: #0f172a;
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #4361ee;
    background: #ffffff;
}

/* Table Styles */
QTableWidget {
    gridline-color: #e2e8f0;
    background: #ffffff;
    alternate-background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    selection-background-color: #4361ee20;
    selection-color: #4361ee;
}

QTableWidget::item {
    padding: 6px;
    border-bottom: 1px solid #e2e8f040;
}

QTableWidget::item:selected {
    background: #4361ee20;
    color: #2c3ebb;
    font-weight: 600;
}

QHeaderView::section {
    background: #4361ee;
    padding: 8px;
    border: none;
    font-weight: 600;
    color: white;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    text-align: center;
    background: #f8fafc;
    height: 14px;
    color: #0f172a;
}

QProgressBar::chunk {
    background: #4361ee;
    border-radius: 3px;
}

/* Status Bar */
QStatusBar {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    color: #0f172a;
}

/* Menu Styles */
QMenuBar {
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    color: #0f172a;
}

QMenuBar::item {
    padding: 6px 10px;
    background: transparent;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background: #4361ee20;
    color: #4361ee;
}

QMenu {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
}

QMenu::item:selected {
    background: #4361ee20;
    color: #4361ee;
}

/* Scrollbar Styles */
QScrollBar:vertical {
    background: #f8fafc;
    width: 10px;
    border-radius: 5px;
}

QScrollBar::handle:vertical {
    background: #e2e8f0;
    border-radius: 5px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #4361ee60;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* ComboBox Styles */
QComboBox {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 12px;
    background: #ffffff;
    min-width: 6em;
}

QComboBox:focus {
    border-color: #4361ee;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

/* Checkbox Styles */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background: #ffffff;
}

QCheckBox::indicator:checked {
    background: #4361ee;
    border-color: #4361ee;
}

QCheckBox::indicator:hover {
    border-color: #4361ee;
}
