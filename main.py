#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队管理工具 - 主入口文件
重构版本 - 模块化架构

作者: Claude 4.0 sonnet
版本: 2.0.0
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import QTimer
from PyQt6.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入重构后的模块
from config import Config
from ui.styles import ThemeManager, StyleManager
from utils import Logger, SecurityManager
from utils.helpers import ConfigHelper


class Application:
    """应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.app = None
        self.main_window = None
        self.config = None
        self.theme_manager = None
        self.security_manager = None
        self.logger = None
        
    def initialize(self):
        """初始化所有组件"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("团队管理工具")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("Team Manager")
            self.app.setOrganizationDomain("teammanager.local")
            
            # 设置应用样式
            self.app.setStyle('Fusion')
            
            # 初始化日志系统
            self.logger = Logger("TeamManager")
            self.logger.info("应用启动", "开始初始化团队管理工具 v2.0")
            
            # 初始化配置系统
            self.config = Config()
            self.logger.info("配置系统", "配置系统初始化完成")
            
            # 初始化安全管理器
            self.security_manager = SecurityManager()
            self.logger.info("安全系统", "安全管理器初始化完成")
            
            # 初始化主题管理器
            self.theme_manager = ThemeManager()
            current_theme = self.config.get('ui.theme', 'light')
            self.theme_manager.apply_theme(current_theme, self.app)
            StyleManager.set_theme_colors(current_theme)
            self.logger.info("主题系统", f"已应用主题: {current_theme}")
            
            # 设置字体
            self._setup_font()
            
            # 创建主窗口（延迟导入避免循环依赖）
            self._create_main_window()
            
            self.logger.success("应用启动", "团队管理工具初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error("应用启动失败", f"初始化过程中发生错误: {str(e)}", exception=e)
            else:
                print(f"❌ 应用启动失败: {str(e)}")
            return False
    
    def _setup_font(self):
        """设置应用字体"""
        try:
            font_size = self.config.get('ui.font_size', 9)
            font = QFont("Microsoft YaHei UI", font_size)
            self.app.setFont(font)
            self.logger.debug("字体设置", f"已设置字体大小: {font_size}")
        except Exception as e:
            self.logger.warning("字体设置", f"字体设置失败，使用默认字体: {str(e)}")
    
    def _create_main_window(self):
        """创建主窗口"""
        try:
            # 延迟导入主窗口类
            from team_manager import TeamManagerMainWindow
            
            # 创建主窗口实例
            self.main_window = TeamManagerMainWindow()
            
            # 传递依赖项
            self.main_window.config = self.config
            self.main_window.theme_manager = self.theme_manager
            self.main_window.security_manager = self.security_manager
            self.main_window.logger = self.logger
            
            # 居中显示窗口
            self._center_window()
            
            # 显示主窗口
            self.main_window.show()
            
            self.logger.info("主窗口", "主窗口创建并显示成功")
            
        except Exception as e:
            self.logger.error("主窗口创建失败", f"无法创建主窗口: {str(e)}", exception=e)
            raise
    
    def _center_window(self):
        """居中显示窗口"""
        try:
            if self.main_window:
                screen = self.app.primaryScreen().geometry()
                window_size = self.main_window.geometry()
                x = (screen.width() - window_size.width()) // 2
                y = (screen.height() - window_size.height()) // 2
                self.main_window.move(x, y)
                self.logger.debug("窗口位置", "窗口已居中显示")
        except Exception as e:
            self.logger.warning("窗口位置", f"窗口居中失败: {str(e)}")
    
    def run(self):
        """运行应用程序"""
        if not self.initialize():
            return 1
        
        try:
            # 显示启动成功消息
            if self.main_window and hasattr(self.main_window, 'status_label'):
                self.main_window.status_label.setText("应用启动成功，请配置API设置后开始使用")
            
            self.logger.info("应用运行", "🎉 团队管理工具启动成功！")
            self.logger.info("使用提示", "💡 请在 工具 -> 配置设置 中配置API信息")
            
            # 运行应用主循环
            return self.app.exec()
            
        except Exception as e:
            self.logger.error("应用运行失败", f"应用运行过程中发生错误: {str(e)}", exception=e)
            return 1
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.config:
                # 保存配置
                if self.config.get('features.auto_save', True):
                    self.config.save_config()
                    self.logger.info("配置保存", "应用配置已自动保存")
            
            if self.logger:
                self.logger.info("应用退出", "团队管理工具正在关闭...")
                
        except Exception as e:
            print(f"❌ 清理资源时发生错误: {str(e)}")


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = f"未捕获的异常: {exc_type.__name__}: {exc_value}"
    print(f"❌ {error_msg}")
    
    # 尝试记录到日志
    try:
        logger = Logger("TeamManager")
        logger.critical("未捕获异常", error_msg, exception=exc_value)
    except:
        pass
    
    # 显示错误对话框
    try:
        from ui.dialogs.message_box import CustomMessageBox
        app = QApplication.instance()
        if app:
            temp_widget = QWidget()
            CustomMessageBox.show_error(
                temp_widget, 
                "严重错误",
                f"应用程序遇到未处理的错误：\n\n{error_msg}\n\n"
                f"请重启应用程序。如果问题持续存在，请联系技术支持。"
            )
            QTimer.singleShot(5000, app.quit)
    except:
        pass


def main():
    """主函数"""
    # 设置全局异常处理器
    sys.excepthook = handle_exception
    
    # 创建并运行应用程序
    app = Application()
    
    try:
        exit_code = app.run()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断，正在退出...")
        exit_code = 0
    except Exception as e:
        print(f"❌ 应用程序异常退出: {str(e)}")
        exit_code = 1
    finally:
        app.cleanup()
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
