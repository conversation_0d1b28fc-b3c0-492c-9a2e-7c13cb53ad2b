#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团队管理工具 - 依赖项安装脚本
自动安装所需的Python包
"""

import sys
import subprocess
import importlib.util

# 必需的依赖项列表
REQUIRED_PACKAGES = [
    "PyQt6",
    "requests", 
    "aiohttp",
    "cachetools",
    "cryptography"
]

# 可选的依赖项（用于增强功能）
OPTIONAL_PACKAGES = [
    "psutil",  # 系统监控
    "pillow",  # 图像处理
    "matplotlib",  # 图表绘制
    "pandas"   # 数据处理
]

def check_package(package_name):
    """
    检查包是否已安装
    
    Args:
        package_name: 包名
        
    Returns:
        bool: 是否已安装
    """
    try:
        importlib.import_module(package_name.lower().replace('-', '_'))
        return True
    except ImportError:
        return False

def install_package(package_name):
    """
    安装单个包
    
    Args:
        package_name: 包名
        
    Returns:
        bool: 安装是否成功
    """
    try:
        print(f"🔄 正在安装 {package_name}...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def upgrade_pip():
    """升级pip到最新版本"""
    try:
        print("🔄 正在升级pip...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
            capture_output=True,
            check=True
        )
        print("✅ pip升级成功")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ pip升级失败，继续使用当前版本")
        return False

def install_requirements():
    """安装所有必需的依赖项"""
    print("🚀 团队管理工具 - 依赖项安装")
    print("=" * 50)
    
    # 升级pip
    upgrade_pip()
    print()
    
    # 检查并安装必需的包
    print("📦 检查必需的依赖项...")
    missing_required = []
    
    for package in REQUIRED_PACKAGES:
        if check_package(package):
            print(f"✅ {package} 已安装")
        else:
            print(f"❌ {package} 未安装")
            missing_required.append(package)
    
    if missing_required:
        print(f"\n🔧 需要安装 {len(missing_required)} 个必需的包...")
        failed_packages = []
        
        for package in missing_required:
            if not install_package(package):
                failed_packages.append(package)
        
        if failed_packages:
            print(f"\n❌ 以下必需的包安装失败: {', '.join(failed_packages)}")
            print("请手动安装这些包或检查网络连接")
            return False
        else:
            print(f"\n✅ 所有必需的包安装完成！")
    else:
        print("\n✅ 所有必需的依赖项都已安装")
    
    # 检查可选的包
    print(f"\n📦 检查可选的依赖项...")
    missing_optional = []
    
    for package in OPTIONAL_PACKAGES:
        if check_package(package):
            print(f"✅ {package} 已安装")
        else:
            print(f"⚪ {package} 未安装 (可选)")
            missing_optional.append(package)
    
    if missing_optional:
        print(f"\n💡 可选包说明:")
        print("  - psutil: 系统性能监控")
        print("  - pillow: 图像处理支持")
        print("  - matplotlib: 数据图表功能")
        print("  - pandas: 高级数据处理")
        
        install_optional = input(f"\n是否安装可选包？(y/N): ").lower().strip()
        
        if install_optional in ['y', 'yes']:
            print(f"\n🔧 安装可选包...")
            for package in missing_optional:
                install_package(package)
        else:
            print("⚪ 跳过可选包安装")
    
    print("\n" + "=" * 50)
    print("🎉 依赖项检查完成！")
    
    # 验证核心功能
    print("\n🔍 验证核心功能...")
    try:
        import PyQt6.QtWidgets
        print("✅ PyQt6 GUI框架正常")
    except ImportError:
        print("❌ PyQt6导入失败")
        return False
    
    try:
        import requests
        print("✅ HTTP请求库正常")
    except ImportError:
        print("❌ requests导入失败")
        return False
    
    try:
        import aiohttp
        print("✅ 异步HTTP库正常")
    except ImportError:
        print("❌ aiohttp导入失败")
        return False
    
    try:
        from cryptography.fernet import Fernet
        print("✅ 加密库正常")
    except ImportError:
        print("❌ cryptography导入失败")
        return False
    
    print("\n✅ 所有核心功能验证通过！")
    print("🚀 现在可以运行团队管理工具了")
    print("\n启动命令:")
    print("  python start_optimized.py  # 使用优化版本")
    print("  python team_manager.py    # 使用原始版本")
    
    return True

def create_requirements_txt():
    """创建requirements.txt文件"""
    requirements_content = """# 团队管理工具依赖项
# 必需的包
PyQt6>=6.0.0
requests>=2.25.0
aiohttp>=3.8.0
cachetools>=4.2.0
cryptography>=3.4.0

# 可选的包（用于增强功能）
# psutil>=5.8.0
# pillow>=8.0.0
# matplotlib>=3.3.0
# pandas>=1.3.0
"""
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        print("📝 已创建 requirements.txt 文件")
        print("   可以使用 'pip install -r requirements.txt' 安装依赖")
    except Exception as e:
        print(f"⚠️ 创建requirements.txt失败: {e}")

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("❌ 需要Python 3.7或更高版本")
            print(f"当前版本: {sys.version}")
            return 1
        
        print(f"✅ Python版本: {sys.version}")
        
        # 安装依赖项
        success = install_requirements()
        
        # 创建requirements.txt
        create_requirements_txt()
        
        if success:
            print(f"\n🎉 安装完成！团队管理工具已准备就绪。")
            return 0
        else:
            print(f"\n❌ 安装过程中遇到问题，请检查错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断安装")
        return 1
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
