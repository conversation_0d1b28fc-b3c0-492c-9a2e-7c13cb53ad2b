# -*- coding: utf-8 -*-
"""
后台任务队列系统
提供异步任务处理和进度跟踪功能
"""

import asyncio
import threading
from enum import Enum
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Task:
    """任务数据类"""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    func: Callable = None
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    result: Any = None
    error: Optional[Exception] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.name and self.func:
            self.name = getattr(self.func, '__name__', 'Unknown Task')
    
    @property
    def duration(self) -> Optional[float]:
        """
        获取任务执行时长（秒）
        
        Returns:
            执行时长或None
        """
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def is_finished(self) -> bool:
        """
        检查任务是否已完成
        
        Returns:
            是否已完成
        """
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


class TaskWorker(QThread):
    """任务工作线程"""
    
    # 信号定义
    task_started = pyqtSignal(str)  # task_id
    task_progress = pyqtSignal(str, float)  # task_id, progress
    task_completed = pyqtSignal(str, object)  # task_id, result
    task_failed = pyqtSignal(str, str)  # task_id, error_message
    
    def __init__(self, task_queue):
        """
        初始化任务工作线程
        
        Args:
            task_queue: 任务队列实例
        """
        super().__init__()
        self.task_queue = task_queue
        self.current_task: Optional[Task] = None
        self.should_stop = False
    
    def run(self):
        """运行工作线程"""
        while not self.should_stop:
            try:
                # 获取下一个任务
                task = self.task_queue.get_next_task()
                if task is None:
                    self.msleep(100)  # 没有任务时短暂休眠
                    continue
                
                self.current_task = task
                self.execute_task(task)
                
            except Exception as e:
                if self.current_task:
                    self.task_failed.emit(self.current_task.id, str(e))
                print(f"❌ 工作线程异常: {e}")
            finally:
                self.current_task = None
    
    def execute_task(self, task: Task):
        """
        执行单个任务
        
        Args:
            task: 任务对象
        """
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            self.task_started.emit(task.id)
            
            # 执行任务函数
            if asyncio.iscoroutinefunction(task.func):
                # 异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(task.func(*task.args, **task.kwargs))
                finally:
                    loop.close()
            else:
                # 同步函数
                result = task.func(*task.args, **task.kwargs)
            
            # 任务完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            self.task_completed.emit(task.id, result)
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = e
            self.task_failed.emit(task.id, str(e))
    
    def stop(self):
        """停止工作线程"""
        self.should_stop = True
        if self.current_task:
            self.current_task.status = TaskStatus.CANCELLED


class TaskQueue(QObject):
    """任务队列管理器"""
    
    # 信号定义
    task_added = pyqtSignal(str)  # task_id
    task_started = pyqtSignal(str)  # task_id
    task_progress = pyqtSignal(str, float)  # task_id, progress
    task_completed = pyqtSignal(str, object)  # task_id, result
    task_failed = pyqtSignal(str, str)  # task_id, error_message
    queue_status_changed = pyqtSignal(int, int)  # pending_count, running_count
    
    def __init__(self, max_workers: int = 3):
        """
        初始化任务队列
        
        Args:
            max_workers: 最大工作线程数
        """
        super().__init__()
        self.max_workers = max_workers
        self.tasks: Dict[str, Task] = {}
        self.pending_tasks: List[str] = []
        self.running_tasks: List[str] = []
        self.workers: List[TaskWorker] = []
        
        # 创建工作线程
        self._create_workers()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._emit_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
    
    def _create_workers(self):
        """创建工作线程"""
        for i in range(self.max_workers):
            worker = TaskWorker(self)
            
            # 连接信号
            worker.task_started.connect(self._on_task_started)
            worker.task_progress.connect(self.task_progress.emit)
            worker.task_completed.connect(self._on_task_completed)
            worker.task_failed.connect(self._on_task_failed)
            
            self.workers.append(worker)
            worker.start()
    
    def add_task(self, func: Callable, *args, name: str = "", description: str = "",
                priority: TaskPriority = TaskPriority.NORMAL, **kwargs) -> str:
        """
        添加任务到队列
        
        Args:
            func: 任务函数
            *args: 位置参数
            name: 任务名称
            description: 任务描述
            priority: 任务优先级
            **kwargs: 关键字参数
            
        Returns:
            任务ID
        """
        task = Task(
            name=name or getattr(func, '__name__', 'Unknown Task'),
            description=description,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        self.tasks[task.id] = task
        
        # 按优先级插入到待处理队列
        self._insert_by_priority(task.id)
        
        self.task_added.emit(task.id)
        return task.id
    
    def _insert_by_priority(self, task_id: str):
        """
        按优先级插入任务到待处理队列
        
        Args:
            task_id: 任务ID
        """
        task = self.tasks[task_id]
        inserted = False
        
        for i, pending_id in enumerate(self.pending_tasks):
            pending_task = self.tasks[pending_id]
            if task.priority.value > pending_task.priority.value:
                self.pending_tasks.insert(i, task_id)
                inserted = True
                break
        
        if not inserted:
            self.pending_tasks.append(task_id)
    
    def get_next_task(self) -> Optional[Task]:
        """
        获取下一个待执行的任务
        
        Returns:
            任务对象或None
        """
        if not self.pending_tasks:
            return None
        
        task_id = self.pending_tasks.pop(0)
        return self.tasks.get(task_id)
    
    def _on_task_started(self, task_id: str):
        """
        任务开始事件处理
        
        Args:
            task_id: 任务ID
        """
        if task_id not in self.running_tasks:
            self.running_tasks.append(task_id)
        self.task_started.emit(task_id)
    
    def _on_task_completed(self, task_id: str, result: Any):
        """
        任务完成事件处理
        
        Args:
            task_id: 任务ID
            result: 任务结果
        """
        if task_id in self.running_tasks:
            self.running_tasks.remove(task_id)
        self.task_completed.emit(task_id, result)
    
    def _on_task_failed(self, task_id: str, error_message: str):
        """
        任务失败事件处理
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
        """
        if task_id in self.running_tasks:
            self.running_tasks.remove(task_id)
        self.task_failed.emit(task_id, error_message)
    
    def _emit_status(self):
        """发射状态更新信号"""
        self.queue_status_changed.emit(len(self.pending_tasks), len(self.running_tasks))
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务对象
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象或None
        """
        return self.tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        if task_id in self.pending_tasks:
            self.pending_tasks.remove(task_id)
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.CANCELLED
            return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取队列统计信息
        
        Returns:
            统计信息字典
        """
        status_counts = {}
        for task in self.tasks.values():
            status = task.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'total_tasks': len(self.tasks),
            'pending_tasks': len(self.pending_tasks),
            'running_tasks': len(self.running_tasks),
            'status_counts': status_counts,
            'max_workers': self.max_workers,
            'active_workers': len([w for w in self.workers if w.current_task])
        }
    
    def clear_completed_tasks(self):
        """清除已完成的任务"""
        completed_ids = [
            task_id for task_id, task in self.tasks.items()
            if task.is_finished
        ]
        
        for task_id in completed_ids:
            del self.tasks[task_id]
    
    def shutdown(self):
        """关闭任务队列"""
        # 停止所有工作线程
        for worker in self.workers:
            worker.stop()
            worker.wait(5000)  # 等待最多5秒
        
        # 停止状态定时器
        self.status_timer.stop()
        
        # 清空队列
        self.pending_tasks.clear()
        self.running_tasks.clear()
        self.tasks.clear()


# 全局任务队列实例
global_task_queue = TaskQueue()


def get_task_queue() -> TaskQueue:
    """
    获取全局任务队列实例
    
    Returns:
        任务队列实例
    """
    return global_task_queue
