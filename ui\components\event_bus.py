# -*- coding: utf-8 -*-
"""
事件总线系统
提供组件间通信机制
"""

from enum import Enum
from typing import Dict, List, Callable, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal
from dataclasses import dataclass
from datetime import datetime


class EventType(Enum):
    """事件类型枚举"""
    
    # 数据相关事件
    DATA_LOADED = "data_loaded"
    DATA_UPDATED = "data_updated"
    DATA_REFRESH_REQUESTED = "data_refresh_requested"
    
    # UI相关事件
    THEME_CHANGED = "theme_changed"
    LANGUAGE_CHANGED = "language_changed"
    WINDOW_RESIZED = "window_resized"
    
    # 用户操作事件
    MEMBER_INVITED = "member_invited"
    MEMBER_DELETED = "member_deleted"
    BATCH_OPERATION_STARTED = "batch_operation_started"
    BATCH_OPERATION_COMPLETED = "batch_operation_completed"
    
    # 系统事件
    CONFIG_CHANGED = "config_changed"
    ERROR_OCCURRED = "error_occurred"
    STATUS_UPDATED = "status_updated"
    
    # API事件
    API_REQUEST_STARTED = "api_request_started"
    API_REQUEST_COMPLETED = "api_request_completed"
    API_REQUEST_FAILED = "api_request_failed"


@dataclass
class Event:
    """事件数据类"""
    
    event_type: EventType
    data: Any = None
    source: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = datetime.now()


class EventBus(QObject):
    """事件总线"""
    
    # Qt信号，用于跨线程通信
    event_emitted = pyqtSignal(object)
    
    def __init__(self):
        """初始化事件总线"""
        super().__init__()
        self._subscribers: Dict[EventType, List[Callable]] = {}
        self._event_history: List[Event] = []
        self._max_history = 1000
        
        # 连接Qt信号到处理方法
        self.event_emitted.connect(self._handle_qt_event)
    
    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]):
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        if callback not in self._subscribers[event_type]:
            self._subscribers[event_type].append(callback)
    
    def unsubscribe(self, event_type: EventType, callback: Callable[[Event], None]):
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self._subscribers:
            if callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)
    
    def emit(self, event_type: EventType, data: Any = None, source: str = None):
        """
        发射事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
        """
        event = Event(event_type, data, source)
        
        # 添加到历史记录
        self._add_to_history(event)
        
        # 使用Qt信号发射事件（支持跨线程）
        self.event_emitted.emit(event)
    
    def _handle_qt_event(self, event: Event):
        """
        处理Qt信号事件
        
        Args:
            event: 事件对象
        """
        if event.event_type in self._subscribers:
            for callback in self._subscribers[event.event_type]:
                try:
                    callback(event)
                except Exception as e:
                    print(f"❌ 事件处理器异常: {e}")
                    # 发射错误事件
                    error_event = Event(
                        EventType.ERROR_OCCURRED,
                        {
                            'error': str(e),
                            'original_event': event,
                            'callback': callback.__name__ if hasattr(callback, '__name__') else str(callback)
                        },
                        'EventBus'
                    )
                    self._add_to_history(error_event)
    
    def _add_to_history(self, event: Event):
        """
        添加事件到历史记录
        
        Args:
            event: 事件对象
        """
        self._event_history.append(event)
        
        # 限制历史记录大小
        if len(self._event_history) > self._max_history:
            self._event_history = self._event_history[-self._max_history:]
    
    def get_event_history(self, event_type: EventType = None, 
                         limit: int = None) -> List[Event]:
        """
        获取事件历史记录
        
        Args:
            event_type: 过滤的事件类型
            limit: 返回数量限制
            
        Returns:
            事件历史列表
        """
        history = self._event_history
        
        # 按事件类型过滤
        if event_type:
            history = [event for event in history if event.event_type == event_type]
        
        # 限制数量
        if limit:
            history = history[-limit:]
        
        return history
    
    def clear_history(self):
        """清空事件历史记录"""
        self._event_history.clear()
    
    def get_subscribers_count(self, event_type: EventType = None) -> int:
        """
        获取订阅者数量
        
        Args:
            event_type: 事件类型，如果为None则返回总数
            
        Returns:
            订阅者数量
        """
        if event_type:
            return len(self._subscribers.get(event_type, []))
        else:
            return sum(len(callbacks) for callbacks in self._subscribers.values())
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取事件总线统计信息
        
        Returns:
            统计信息字典
        """
        # 统计各类型事件数量
        event_counts = {}
        for event in self._event_history:
            event_type = event.event_type.value
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        # 统计订阅者信息
        subscriber_counts = {}
        for event_type, callbacks in self._subscribers.items():
            subscriber_counts[event_type.value] = len(callbacks)
        
        return {
            'total_events': len(self._event_history),
            'event_counts': event_counts,
            'subscriber_counts': subscriber_counts,
            'total_subscribers': self.get_subscribers_count(),
            'max_history': self._max_history
        }


class EventSubscriber:
    """事件订阅者基类"""
    
    def __init__(self, event_bus: EventBus):
        """
        初始化事件订阅者
        
        Args:
            event_bus: 事件总线实例
        """
        self.event_bus = event_bus
        self._subscriptions: List[tuple] = []
    
    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]):
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.event_bus.subscribe(event_type, callback)
        self._subscriptions.append((event_type, callback))
    
    def unsubscribe_all(self):
        """取消所有订阅"""
        for event_type, callback in self._subscriptions:
            self.event_bus.unsubscribe(event_type, callback)
        self._subscriptions.clear()
    
    def emit(self, event_type: EventType, data: Any = None, source: str = None):
        """
        发射事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
        """
        self.event_bus.emit(event_type, data, source or self.__class__.__name__)


# 全局事件总线实例
global_event_bus = EventBus()


def get_event_bus() -> EventBus:
    """
    获取全局事件总线实例
    
    Returns:
        事件总线实例
    """
    return global_event_bus


# 便捷的装饰器
def event_handler(event_type: EventType):
    """
    事件处理器装饰器
    
    Args:
        event_type: 事件类型
    """
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            # 自动订阅事件
            if hasattr(self, 'event_bus'):
                self.event_bus.subscribe(event_type, func)
            return func(self, *args, **kwargs)
        return wrapper
    return decorator
