# -*- coding: utf-8 -*-
"""
安全管理模块
提供数据加密、解密和安全验证功能
"""

import base64
import hashlib
import secrets
from typing import Op<PERSON>, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class SecurityManager:
    """安全管理器"""
    
    def __init__(self, master_key: Optional[str] = None):
        """
        初始化安全管理器
        
        Args:
            master_key: 主密钥，如果为None则自动生成
        """
        self.master_key = master_key or self._generate_master_key()
        self._cipher_suite = None
    
    def _generate_master_key(self) -> str:
        """
        生成主密钥
        
        Returns:
            Base64编码的主密钥
        """
        key = Fernet.generate_key()
        return base64.urlsafe_b64encode(key).decode()
    
    def _get_cipher_suite(self, password: str = None) -> Fernet:
        """
        获取加密套件
        
        Args:
            password: 密码，用于派生密钥
            
        Returns:
            Fernet加密套件
        """
        if self._cipher_suite is None:
            if password:
                # 使用密码派生密钥
                salt = b'team_manager_salt'  # 在实际应用中应该使用随机盐
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            else:
                # 使用主密钥
                key = base64.urlsafe_b64decode(self.master_key.encode())
            
            self._cipher_suite = Fernet(key)
        
        return self._cipher_suite
    
    def encrypt_data(self, data: str, password: str = None) -> str:
        """
        加密数据
        
        Args:
            data: 要加密的数据
            password: 可选密码
            
        Returns:
            加密后的Base64字符串
        """
        try:
            cipher_suite = self._get_cipher_suite(password)
            encrypted_data = cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            print(f"❌ 数据加密失败: {e}")
            return data  # 如果加密失败，返回原始数据
    
    def decrypt_data(self, encrypted_data: str, password: str = None) -> str:
        """
        解密数据
        
        Args:
            encrypted_data: 加密的Base64字符串
            password: 可选密码
            
        Returns:
            解密后的原始数据
        """
        try:
            cipher_suite = self._get_cipher_suite(password)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            print(f"❌ 数据解密失败: {e}")
            return encrypted_data  # 如果解密失败，返回原始数据
    
    def encrypt_cookie(self, cookie: str) -> str:
        """
        加密Cookie
        
        Args:
            cookie: 原始Cookie字符串
            
        Returns:
            加密后的Cookie
        """
        if not cookie:
            return cookie
        
        # 为Cookie使用特殊的加密方式
        return self.encrypt_data(cookie, "cookie_encryption_key")
    
    def decrypt_cookie(self, encrypted_cookie: str) -> str:
        """
        解密Cookie
        
        Args:
            encrypted_cookie: 加密的Cookie
            
        Returns:
            原始Cookie字符串
        """
        if not encrypted_cookie:
            return encrypted_cookie
        
        return self.decrypt_data(encrypted_cookie, "cookie_encryption_key")
    
    def hash_password(self, password: str) -> str:
        """
        哈希密码
        
        Args:
            password: 原始密码
            
        Returns:
            哈希后的密码
        """
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """
        验证密码
        
        Args:
            password: 原始密码
            hashed_password: 哈希后的密码
            
        Returns:
            密码是否正确
        """
        try:
            salt, stored_hash = hashed_password.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash.hex() == stored_hash
        except Exception:
            return False
    
    def generate_session_token(self) -> str:
        """
        生成会话令牌
        
        Returns:
            安全的会话令牌
        """
        return secrets.token_urlsafe(32)
    
    def validate_session_token(self, token: str) -> bool:
        """
        验证会话令牌格式
        
        Args:
            token: 会话令牌
            
        Returns:
            令牌是否有效
        """
        if not token or len(token) < 16:
            return False
        
        try:
            # 尝试解码Base64
            base64.urlsafe_b64decode(token + '==')
            return True
        except Exception:
            return False
    
    def sanitize_input(self, input_data: str) -> str:
        """
        清理输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            清理后的数据
        """
        if not input_data:
            return input_data
        
        # 移除潜在的危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
        cleaned_data = input_data
        
        for char in dangerous_chars:
            cleaned_data = cleaned_data.replace(char, '')
        
        return cleaned_data.strip()
    
    def validate_email_security(self, email: str) -> Tuple[bool, str]:
        """
        验证邮箱安全性
        
        Args:
            email: 邮箱地址
            
        Returns:
            (是否安全, 错误信息)
        """
        if not email:
            return False, "邮箱不能为空"
        
        # 基本格式检查
        if '@' not in email or '.' not in email:
            return False, "邮箱格式无效"
        
        # 检查危险字符
        dangerous_patterns = ['<script', 'javascript:', 'data:', 'vbscript:']
        email_lower = email.lower()
        
        for pattern in dangerous_patterns:
            if pattern in email_lower:
                return False, f"邮箱包含危险内容: {pattern}"
        
        # 长度检查
        if len(email) > 254:  # RFC 5321 限制
            return False, "邮箱地址过长"
        
        return True, ""
    
    def create_secure_config(self, config_data: dict) -> dict:
        """
        创建安全配置
        
        Args:
            config_data: 原始配置数据
            
        Returns:
            安全处理后的配置数据
        """
        secure_config = config_data.copy()
        
        # 加密敏感信息
        if 'api' in secure_config and 'headers' in secure_config['api']:
            headers = secure_config['api']['headers']
            
            # 加密Cookie
            if 'cookie' in headers and headers['cookie']:
                headers['cookie'] = self.encrypt_cookie(headers['cookie'])
                print("🔒 Cookie已加密")
        
        return secure_config
    
    def load_secure_config(self, config_data: dict) -> dict:
        """
        加载安全配置
        
        Args:
            config_data: 加密的配置数据
            
        Returns:
            解密后的配置数据
        """
        secure_config = config_data.copy()
        
        # 解密敏感信息
        if 'api' in secure_config and 'headers' in secure_config['api']:
            headers = secure_config['api']['headers']
            
            # 解密Cookie
            if 'cookie' in headers and headers['cookie']:
                headers['cookie'] = self.decrypt_cookie(headers['cookie'])
                print("🔓 Cookie已解密")
        
        return secure_config
    
    @staticmethod
    def is_secure_connection(url: str) -> bool:
        """
        检查是否为安全连接
        
        Args:
            url: URL地址
            
        Returns:
            是否为HTTPS连接
        """
        return url.lower().startswith('https://')
    
    def get_security_report(self) -> dict:
        """
        获取安全报告
        
        Returns:
            安全状态报告
        """
        return {
            'encryption_enabled': True,
            'master_key_length': len(self.master_key),
            'cipher_suite_available': self._cipher_suite is not None,
            'security_level': 'HIGH',
            'recommendations': [
                '定期更换主密钥',
                '使用强密码保护配置',
                '启用HTTPS连接',
                '定期备份加密配置'
            ]
        }
