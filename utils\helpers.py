# -*- coding: utf-8 -*-
"""
辅助工具模块
提供各种实用的辅助函数
"""

import re
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse


class EmailValidator:
    """邮箱验证器"""
    
    # 邮箱正则表达式
    EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    )
    
    # 常见邮箱域名
    COMMON_DOMAINS = {
        'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com',
        'qq.com', '163.com', '126.com', 'sina.com', 'sohu.com'
    }
    
    @classmethod
    def validate_email(cls, email: str) -> Tuple[bool, str]:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            (是否有效, 错误信息)
        """
        if not email:
            return False, "邮箱不能为空"
        
        email = email.strip().lower()
        
        # 长度检查
        if len(email) > 254:
            return False, "邮箱地址过长"
        
        # 格式检查
        if not cls.EMAIL_PATTERN.match(email):
            return False, "邮箱格式无效"
        
        # 分割检查
        try:
            local, domain = email.split('@')
            
            # 本地部分检查
            if len(local) > 64:
                return False, "邮箱用户名部分过长"
            
            if local.startswith('.') or local.endswith('.'):
                return False, "邮箱用户名不能以点开头或结尾"
            
            if '..' in local:
                return False, "邮箱用户名不能包含连续的点"
            
            # 域名部分检查
            if len(domain) > 253:
                return False, "邮箱域名过长"
            
            if domain.startswith('.') or domain.endswith('.'):
                return False, "邮箱域名格式无效"
            
            if '..' in domain:
                return False, "邮箱域名不能包含连续的点"
            
        except ValueError:
            return False, "邮箱格式无效"
        
        return True, ""
    
    @classmethod
    def validate_email_list(cls, emails: List[str]) -> Dict[str, List[str]]:
        """
        批量验证邮箱列表
        
        Args:
            emails: 邮箱列表
            
        Returns:
            验证结果字典 {'valid': [...], 'invalid': [...], 'duplicates': [...]}
        """
        valid_emails = []
        invalid_emails = []
        seen_emails = set()
        duplicates = []
        
        for email in emails:
            email = email.strip().lower()
            
            # 检查重复
            if email in seen_emails:
                duplicates.append(email)
                continue
            
            seen_emails.add(email)
            
            # 验证格式
            is_valid, error = cls.validate_email(email)
            if is_valid:
                valid_emails.append(email)
            else:
                invalid_emails.append(f"{email}: {error}")
        
        return {
            'valid': valid_emails,
            'invalid': invalid_emails,
            'duplicates': duplicates
        }
    
    @classmethod
    def suggest_corrections(cls, email: str) -> List[str]:
        """
        建议邮箱修正
        
        Args:
            email: 可能有错误的邮箱
            
        Returns:
            建议的修正列表
        """
        suggestions = []
        
        if not email or '@' not in email:
            return suggestions
        
        try:
            local, domain = email.split('@', 1)
            
            # 常见域名拼写错误修正
            domain_corrections = {
                'gmai.com': 'gmail.com',
                'gmial.com': 'gmail.com',
                'gmail.co': 'gmail.com',
                'outlok.com': 'outlook.com',
                'outloo.com': 'outlook.com',
                'hotmai.com': 'hotmail.com',
                'yaho.com': 'yahoo.com',
                'yahooo.com': 'yahoo.com',
            }
            
            if domain.lower() in domain_corrections:
                suggestions.append(f"{local}@{domain_corrections[domain.lower()]}")
            
            # 检查是否缺少常见域名后缀
            if '.' not in domain:
                for common_domain in cls.COMMON_DOMAINS:
                    if domain.lower() in common_domain:
                        suggestions.append(f"{local}@{common_domain}")
        
        except ValueError:
            pass
        
        return suggestions


class DateTimeHelper:
    """日期时间辅助工具"""
    
    @staticmethod
    def format_datetime(dt: datetime, format_type: str = "default") -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_type: 格式类型
            
        Returns:
            格式化后的字符串
        """
        if not dt:
            return "N/A"
        
        formats = {
            "default": "%Y-%m-%d %H:%M:%S",
            "date": "%Y-%m-%d",
            "time": "%H:%M:%S",
            "friendly": "%m月%d日 %H:%M",
            "iso": "%Y-%m-%dT%H:%M:%S",
            "compact": "%Y%m%d_%H%M%S"
        }
        
        format_str = formats.get(format_type, formats["default"])
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(date_str: str) -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            
        Returns:
            日期时间对象或None
        """
        if not date_str:
            return None
        
        # 常见格式列表
        formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d",
            "%m/%d/%Y",
            "%d/%m/%Y",
            "%Y年%m月%d日",
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    @staticmethod
    def get_relative_time(dt: datetime) -> str:
        """
        获取相对时间描述
        
        Args:
            dt: 日期时间对象
            
        Returns:
            相对时间描述
        """
        if not dt:
            return "未知时间"
        
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    
    @staticmethod
    def is_today(dt: datetime) -> bool:
        """
        检查是否为今天
        
        Args:
            dt: 日期时间对象
            
        Returns:
            是否为今天
        """
        if not dt:
            return False
        return dt.date() == datetime.now().date()
    
    @staticmethod
    def get_date_range(days: int) -> Tuple[datetime, datetime]:
        """
        获取日期范围
        
        Args:
            days: 天数
            
        Returns:
            (开始日期, 结束日期)
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        return start_date, end_date


class DataFormatter:
    """数据格式化工具"""
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def format_number(number: int) -> str:
        """
        格式化数字（添加千分位分隔符）
        
        Args:
            number: 数字
            
        Returns:
            格式化后的数字字符串
        """
        return f"{number:,}"
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
        """
        截断文本
        
        Args:
            text: 原始文本
            max_length: 最大长度
            suffix: 后缀
            
        Returns:
            截断后的文本
        """
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix


class URLHelper:
    """URL辅助工具"""
    
    @staticmethod
    def validate_url(url: str) -> Tuple[bool, str]:
        """
        验证URL格式
        
        Args:
            url: URL字符串
            
        Returns:
            (是否有效, 错误信息)
        """
        if not url:
            return False, "URL不能为空"
        
        try:
            result = urlparse(url)
            if not all([result.scheme, result.netloc]):
                return False, "URL格式无效"
            
            if result.scheme not in ['http', 'https']:
                return False, "URL协议必须是http或https"
            
            return True, ""
        except Exception as e:
            return False, f"URL解析错误: {str(e)}"
    
    @staticmethod
    def is_secure_url(url: str) -> bool:
        """
        检查是否为安全URL（HTTPS）
        
        Args:
            url: URL字符串
            
        Returns:
            是否为HTTPS
        """
        try:
            return urlparse(url).scheme == 'https'
        except:
            return False
    
    @staticmethod
    def extract_domain(url: str) -> str:
        """
        提取URL的域名
        
        Args:
            url: URL字符串
            
        Returns:
            域名
        """
        try:
            return urlparse(url).netloc
        except:
            return ""


class ConfigHelper:
    """配置辅助工具"""
    
    @staticmethod
    def merge_configs(base_config: Dict, user_config: Dict) -> Dict:
        """
        深度合并配置字典
        
        Args:
            base_config: 基础配置
            user_config: 用户配置
            
        Returns:
            合并后的配置
        """
        result = base_config.copy()
        
        for key, value in user_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = ConfigHelper.merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    @staticmethod
    def validate_config_structure(config: Dict, required_keys: List[str]) -> Tuple[bool, List[str]]:
        """
        验证配置结构
        
        Args:
            config: 配置字典
            required_keys: 必需的键列表（支持点分隔路径）
            
        Returns:
            (是否有效, 缺失的键列表)
        """
        missing_keys = []
        
        for key_path in required_keys:
            keys = key_path.split('.')
            current = config
            
            for key in keys:
                if not isinstance(current, dict) or key not in current:
                    missing_keys.append(key_path)
                    break
                current = current[key]
        
        return len(missing_keys) == 0, missing_keys
